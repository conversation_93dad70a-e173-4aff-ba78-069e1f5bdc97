(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},616:(e,s,a)=>{"use strict";a.d(s,{default:()=>c});var t=a(5155),l=a(6874),n=a.n(l),r=a(2115);let i=r.forwardRef(function(e,s){let{title:a,titleId:t,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},l),a?r.createElement("title",{id:t},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}),d=r.forwardRef(function(e,s){let{title:a,titleId:t,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},l),a?r.createElement("title",{id:t},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),o=[{name:"Home",href:"/"},{name:"About Us",href:"/about"},{name:"Academics",href:"/academics"},{name:"Activities",href:"/activities"},{name:"Admissions",href:"/admissions"},{name:"Faculty",href:"/faculty"},{name:"Gallery",href:"/gallery"},{name:"News & Events",href:"/news-events"},{name:"Student Corner",href:"/student-corner"},{name:"Parent Zone",href:"/parent-zone"},{name:"Contact",href:"/contact"}];function c(){let[e,s]=(0,r.useState)(!1);return(0,t.jsxs)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:[(0,t.jsx)("nav",{className:"mx-auto max-w-7xl px-6 lg:px-8","aria-label":"Top",children:(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,t.jsx)("div",{className:"flex lg:flex-1",children:(0,t.jsxs)(n(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,t.jsx)("span",{className:"sr-only",children:"Sarasvati School"}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-lg",children:"S"})}),(0,t.jsxs)("div",{className:"hidden sm:block",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Sarasvati School"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Excellence in Education"})]})]})]})}),(0,t.jsx)("div",{className:"hidden lg:flex lg:gap-x-8",children:o.map(e=>(0,t.jsx)(n(),{href:e.href,className:"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors duration-200",children:e.name},e.name))}),(0,t.jsxs)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4",children:[(0,t.jsx)(n(),{href:"/admissions",className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 transition-colors duration-200",children:"Apply Now"}),(0,t.jsx)(n(),{href:"/contact",className:"rounded-md border border-gray-300 px-4 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50 transition-colors duration-200",children:"Contact Us"})]}),(0,t.jsx)("div",{className:"flex lg:hidden",children:(0,t.jsxs)("button",{type:"button",className:"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700",onClick:()=>s(!0),children:[(0,t.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,t.jsx)(i,{className:"h-6 w-6","aria-hidden":"true"})]})})]})}),e&&(0,t.jsxs)("div",{className:"lg:hidden",children:[(0,t.jsx)("div",{className:"fixed inset-0 z-50"}),(0,t.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,t.jsx)("span",{className:"sr-only",children:"Sarasvati School"}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold",children:"S"})}),(0,t.jsx)("div",{children:(0,t.jsx)("h1",{className:"text-lg font-bold text-gray-900",children:"Sarasvati School"})})]})]}),(0,t.jsxs)("button",{type:"button",className:"-m-2.5 rounded-md p-2.5 text-gray-700",onClick:()=>s(!1),children:[(0,t.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,t.jsx)(d,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,t.jsx)("div",{className:"mt-6 flow-root",children:(0,t.jsxs)("div",{className:"-my-6 divide-y divide-gray-500/10",children:[(0,t.jsx)("div",{className:"space-y-2 py-6",children:o.map(e=>(0,t.jsx)(n(),{href:e.href,className:"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50",onClick:()=>s(!1),children:e.name},e.name))}),(0,t.jsxs)("div",{className:"py-6 space-y-2",children:[(0,t.jsx)(n(),{href:"/admissions",className:"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-white bg-blue-600 hover:bg-blue-500",onClick:()=>s(!1),children:"Apply Now"}),(0,t.jsx)(n(),{href:"/contact",className:"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 border border-gray-300 hover:bg-gray-50",onClick:()=>s(!1),children:"Contact Us"})]})]})})]})]})]})}},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},2076:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.t.bind(a,1666,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,616))}},e=>{var s=s=>e(e.s=s);e.O(0,[258,874,441,684,358],()=>s(2076)),_N_E=e.O()}]);