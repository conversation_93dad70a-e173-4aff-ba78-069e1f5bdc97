(()=>{var e={};e.id=125,e.ids=[125],e.modules={137:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(3210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))})},440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1022:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(3210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))})},1245:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(3210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},2672:(e,s,t)=>{Promise.resolve().then(t.bind(t,3635))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3635:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(687),r=t(3210),l=t(8089),i=t(1245),n=t(8445),d=t(1022),o=t(137),c=t(4859),m=t(6942);let x=[{standard:"Nursery",age:"3+ years",cutoffDate:"As on 31st March"},{standard:"LKG",age:"4+ years",cutoffDate:"As on 31st March"},{standard:"UKG",age:"5+ years",cutoffDate:"As on 31st March"},{standard:"Class I",age:"6+ years",cutoffDate:"As on 31st March"},{standard:"Class II-V",age:"Age appropriate",cutoffDate:"Based on previous class"},{standard:"Class VI-VIII",age:"Age appropriate",cutoffDate:"Based on previous class"},{standard:"Class IX",age:"14+ years",cutoffDate:"Based on Class VIII completion"},{standard:"Class XI",age:"16+ years",cutoffDate:"Based on Class X results"}],h=["Birth Certificate (Original + 2 copies)","Previous School Transfer Certificate","Previous School Report Card/Mark Sheet","Aadhar Card of Student (Copy)","Aadhar Card of Parents (Copy)","Passport Size Photographs (6 copies)","Medical Certificate from Registered Doctor","Caste Certificate (if applicable)","Income Certificate (for fee concession)","Address Proof (Electricity Bill/Rent Agreement)"],u=[{standard:"Nursery - UKG",admission:"₹5,000",tuition:"₹3,500",annual:"₹2,000",total:"₹10,500"},{standard:"Class I - V",admission:"₹7,000",tuition:"₹4,500",annual:"₹2,500",total:"₹14,000"},{standard:"Class VI - VIII",admission:"₹8,000",tuition:"₹5,500",annual:"₹3,000",total:"₹16,500"},{standard:"Class IX - X",admission:"₹10,000",tuition:"₹6,500",annual:"₹3,500",total:"₹20,000"},{standard:"Class XI - XII",admission:"₹12,000",tuition:"₹7,500",annual:"₹4,000",total:"₹23,500"}];function p(){let[e,s]=(0,r.useState)({studentName:"",dateOfBirth:"",gender:"",standard:"",previousSchool:"",fatherName:"",motherName:"",phone:"",email:"",address:""}),t=t=>{s({...e,[t.target.name]:t.target.value})};return(0,a.jsxs)("div",{className:"bg-white",children:[(0,a.jsx)("section",{className:"relative bg-gradient-to-br from-blue-50 via-white to-green-50",children:(0,a.jsx)("div",{className:"mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8",children:(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",children:(0,a.jsx)("span",{className:"gradient-text",children:"Admissions Open"})}),(0,a.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:"Join the Sarasvati School family and embark on a journey of academic excellence, character development, and holistic growth."}),(0,a.jsx)("div",{className:"mt-10",children:(0,a.jsxs)("div",{className:"inline-flex items-center rounded-full bg-green-100 px-6 py-3",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)("span",{className:"text-green-800 font-semibold",children:"Academic Year 2024-25 Admissions Open"})]})})]})})}),(0,a.jsx)("section",{className:"section-padding",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Admission Process"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Simple and transparent admission process designed for your convenience"})]}),(0,a.jsxs)("div",{className:"mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-4",children:[(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)("span",{className:"text-2xl font-bold text-blue-600",children:"1"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Application"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Fill out the online application form or visit our campus"})]}),(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)("span",{className:"text-2xl font-bold text-green-600",children:"2"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Document Verification"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Submit required documents for verification"})]}),(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)("span",{className:"text-2xl font-bold text-yellow-600",children:"3"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Interaction"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Student and parent interaction with school counselor"})]}),(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-purple-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)("span",{className:"text-2xl font-bold text-purple-600",children:"4"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Admission"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Fee payment and admission confirmation"})]})]})]})}),(0,a.jsx)("section",{className:"section-padding bg-gray-50",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Age Criteria"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Age requirements for admission to different standards"})]}),(0,a.jsx)("div",{className:"mx-auto mt-16 max-w-4xl",children:(0,a.jsxs)("div",{className:"card overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-blue-600 px-6 py-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-white font-semibold",children:[(0,a.jsx)("div",{children:"Standard"}),(0,a.jsx)("div",{children:"Minimum Age"}),(0,a.jsx)("div",{children:"Cut-off Date"})]})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:x.map((e,s)=>(0,a.jsx)("div",{className:`px-6 py-4 ${s%2==0?"bg-white":"bg-gray-50"}`,children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.standard}),(0,a.jsx)("div",{className:"text-gray-600",children:e.age}),(0,a.jsx)("div",{className:"text-gray-600",children:e.cutoffDate})]})},e.standard))})]})})]})}),(0,a.jsx)("section",{className:"section-padding",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Online Application Form"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Fill out the form below to start your admission process"})]}),(0,a.jsx)("div",{className:"mx-auto mt-16 max-w-2xl",children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),alert("Application submitted successfully! We will contact you soon.")},className:"card p-8 space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"studentName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Student Name *"}),(0,a.jsx)("input",{type:"text",id:"studentName",name:"studentName",required:!0,value:e.studentName,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"dateOfBirth",className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),(0,a.jsx)("input",{type:"date",id:"dateOfBirth",name:"dateOfBirth",required:!0,value:e.dateOfBirth,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"gender",className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender *"}),(0,a.jsxs)("select",{id:"gender",name:"gender",required:!0,value:e.gender,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Gender"}),(0,a.jsx)("option",{value:"male",children:"Male"}),(0,a.jsx)("option",{value:"female",children:"Female"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"standard",className:"block text-sm font-medium text-gray-700 mb-2",children:"Applying for Standard *"}),(0,a.jsxs)("select",{id:"standard",name:"standard",required:!0,value:e.standard,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Standard"}),(0,a.jsx)("option",{value:"nursery",children:"Nursery"}),(0,a.jsx)("option",{value:"lkg",children:"LKG"}),(0,a.jsx)("option",{value:"ukg",children:"UKG"}),(0,a.jsx)("option",{value:"class1",children:"Class I"}),(0,a.jsx)("option",{value:"class2",children:"Class II"}),(0,a.jsx)("option",{value:"class3",children:"Class III"}),(0,a.jsx)("option",{value:"class4",children:"Class IV"}),(0,a.jsx)("option",{value:"class5",children:"Class V"}),(0,a.jsx)("option",{value:"class6",children:"Class VI"}),(0,a.jsx)("option",{value:"class7",children:"Class VII"}),(0,a.jsx)("option",{value:"class8",children:"Class VIII"}),(0,a.jsx)("option",{value:"class9",children:"Class IX"}),(0,a.jsx)("option",{value:"class10",children:"Class X"}),(0,a.jsx)("option",{value:"class11",children:"Class XI"}),(0,a.jsx)("option",{value:"class12",children:"Class XII"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"previousSchool",className:"block text-sm font-medium text-gray-700 mb-2",children:"Previous School"}),(0,a.jsx)("input",{type:"text",id:"previousSchool",name:"previousSchool",value:e.previousSchool,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fatherName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Father's Name *"}),(0,a.jsx)("input",{type:"text",id:"fatherName",name:"fatherName",required:!0,value:e.fatherName,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"motherName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Mother's Name *"}),(0,a.jsx)("input",{type:"text",id:"motherName",name:"motherName",required:!0,value:e.motherName,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number *"}),(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",required:!0,value:e.phone,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",required:!0,value:e.email,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-2",children:"Address *"}),(0,a.jsx)("textarea",{id:"address",name:"address",required:!0,rows:3,value:e.address,onChange:t,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{type:"submit",className:"btn-primary px-8 py-3 text-lg",children:"Submit Application"})})]})})]})}),(0,a.jsx)("section",{className:"section-padding bg-gray-50",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Documents Required"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Please ensure all documents are ready for a smooth admission process"})]}),(0,a.jsx)("div",{className:"mx-auto mt-16 max-w-4xl",children:(0,a.jsxs)("div",{className:"card p-8",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:h.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},s))}),(0,a.jsx)("div",{className:"mt-8 p-4 bg-yellow-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("strong",{children:"Note:"})," All documents should be original for verification. Photocopies will be retained by the school. Documents in regional languages should be accompanied by certified English translations."]})})]})})]})}),(0,a.jsx)("section",{className:"section-padding",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Fee Structure"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Transparent and affordable fee structure for quality education"})]}),(0,a.jsxs)("div",{className:"mx-auto mt-16 max-w-6xl",children:[(0,a.jsxs)("div",{className:"card overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-blue-600 px-6 py-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-4 text-white font-semibold",children:[(0,a.jsx)("div",{children:"Standard"}),(0,a.jsx)("div",{children:"Admission Fee"}),(0,a.jsx)("div",{children:"Monthly Tuition"}),(0,a.jsx)("div",{children:"Annual Charges"}),(0,a.jsx)("div",{children:"Total (First Month)"})]})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:u.map((e,s)=>(0,a.jsx)("div",{className:`px-6 py-4 ${s%2==0?"bg-white":"bg-gray-50"}`,children:(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-4",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.standard}),(0,a.jsx)("div",{className:"text-gray-600",children:e.admission}),(0,a.jsx)("div",{className:"text-gray-600",children:e.tuition}),(0,a.jsx)("div",{className:"text-gray-600",children:e.annual}),(0,a.jsx)("div",{className:"font-semibold text-blue-600",children:e.total})]})},e.standard))})]}),(0,a.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-green-600 mx-auto mb-3"}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Payment Options"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Cash, Cheque, Online Transfer, UPI"})]}),(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-blue-600 mx-auto mb-3"}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Payment Schedule"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Monthly, Quarterly, or Annual"})]}),(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)(l.A,{className:"h-8 w-8 text-purple-600 mx-auto mb-3"}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Scholarships"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Merit-based and need-based available"})]})]})]})]})}),(0,a.jsx)("section",{className:"section-padding bg-blue-600",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl",children:"Need Help with Admissions?"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-blue-100",children:"Our admission team is here to assist you throughout the process"})]}),(0,a.jsxs)("div",{className:"mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-white mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Call Us"}),(0,a.jsx)("p",{className:"text-blue-100",children:"+91 98765 43210"}),(0,a.jsx)("p",{className:"text-blue-100",children:"+91 98765 43211"})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 text-white mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Email Us"}),(0,a.jsx)("p",{className:"text-blue-100",children:"<EMAIL>"}),(0,a.jsx)("p",{className:"text-blue-100",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-white mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Visit Us"}),(0,a.jsx)("p",{className:"text-blue-100",children:"123 Education Street"}),(0,a.jsx)("p",{className:"text-blue-100",children:"Knowledge City, KC 12345"})]})]}),(0,a.jsxs)("div",{className:"mt-12 text-center",children:[(0,a.jsx)("p",{className:"text-blue-100 mb-4",children:"Admission Office Hours"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:"Monday - Friday: 9:00 AM - 4:00 PM"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:"Saturday: 9:00 AM - 1:00 PM"})]})]})})]})}},3873:e=>{"use strict";e.exports=require("path")},4859:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(3210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},6245:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\admissions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\admissions\\page.tsx","default")},6942:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(3210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))})},7824:(e,s,t)=>{Promise.resolve().then(t.bind(t,6245))},8089:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(3210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},8445:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(3210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 8.25H9m6 3H9m3 6-3-3h1.5a3 3 0 1 0 0-6M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9348:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=t(5239),r=t(8088),l=t(8170),i=t.n(l),n=t(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o={children:["",{children:["admissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6245)),"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\admissions\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8042)),"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\admissions\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admissions/page",pathname:"/admissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,727,658,158],()=>t(9348));module.exports=a})();