(()=>{var e={};e.id=60,e.ids=[60],e.modules={440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1022:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(3210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))})},1245:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(3210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},1679:(e,t,a)=>{Promise.resolve().then(a.bind(a,9982))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3634:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=a(5239),s=a(8088),n=a(8170),i=a.n(n),l=a(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let c={children:["",{children:["parent-zone",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,9982)),"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\parent-zone\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,8042)),"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\parent-zone\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/parent-zone/page",pathname:"/parent-zone",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3873:e=>{"use strict";e.exports=require("path")},4534:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var r=a(687),s=a(3210);let n=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))}),i=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5"}))});var l=a(1245);let o=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))});var c=a(1022),d=a(8445),m=a(5428),u=a(6016),h=a(8089);let x=[{id:1,title:"Fee Payment Reminder",date:"2024-08-05",content:"This is a reminder that the second quarter fees are due by August 15, 2024. Please ensure timely payment to avoid late fees. Payments can be made online through the school portal or at the accounts office during working hours.",category:"Fees",priority:"high"},{id:2,title:"Parent-Teacher Meeting Schedule",date:"2024-08-10",content:"The Parent-Teacher Meeting for Classes I-V will be held on August 30, 2024, from 10:00 AM to 2:00 PM. Parents are requested to meet their child's class teacher as per the time slots that will be shared via email.",category:"Meeting",priority:"medium"},{id:3,title:"School Uniform Update",date:"2024-08-12",content:"The school uniform shop will remain open on all Saturdays of August from 9:00 AM to 1:00 PM for parents to purchase new uniforms or replacements. Winter uniforms will be available from September 15, 2024.",category:"General",priority:"low"},{id:4,title:"Annual Health Check-up",date:"2024-08-15",content:"The annual health check-up for all students will be conducted from August 25-30, 2024. Please ensure your child is present on the designated day. The schedule for each class will be shared via email.",category:"Health",priority:"medium"}],p=[{id:1,title:"Revised School Timings",date:"2024-08-01",reference:"SSPS/2024-25/C-001",content:"This is to inform all parents that the school timings will be revised from August 15, 2024. The new timings will be 8:30 AM to 3:00 PM for all classes. This change is being implemented to optimize the learning hours and reduce traffic congestion during arrival and departure times.",attachmentName:"Revised_Timings_Circular.pdf"},{id:2,title:"Annual Day Celebration",date:"2024-08-08",reference:"SSPS/2024-25/C-002",content:"We are pleased to announce that the Annual Day celebration will be held on September 25, 2024, at the school auditorium. Students participating in the cultural programs will have practice sessions after school hours. Detailed information regarding the schedule and arrangements will be shared soon.",attachmentName:"Annual_Day_Circular.pdf"},{id:3,title:"Introduction of New Extracurricular Activities",date:"2024-08-12",reference:"SSPS/2024-25/C-003",content:"The school is introducing new extracurricular activities for the academic year 2024-25, including Robotics, Western Dance, and Public Speaking. Interested students can register for these activities by filling out the attached form and submitting it to their class teacher by August 20, 2024.",attachmentName:"New_Activities_Form.pdf"}],g=[{question:"How can I check my child's academic progress?",answer:"You can check your child's academic progress through the Parent Portal. Regular updates on assessments, test scores, and teacher comments are available. Additionally, report cards are issued at the end of each term, and parent-teacher meetings are scheduled periodically."},{question:"What is the school's policy on attendance?",answer:"Students are expected to maintain at least 85% attendance throughout the academic year. In case of absence, parents should inform the class teacher through a written note or email. For extended absences due to medical reasons, a doctor's certificate should be submitted."},{question:"How can I pay the school fees?",answer:"School fees can be paid online through the Parent Portal using credit/debit cards or net banking. Alternatively, you can pay at the school accounts office by cash, cheque, or demand draft during working hours (9:00 AM to 2:00 PM on weekdays)."},{question:"What is the procedure for applying for leave?",answer:"For short leaves (1-2 days), a written note in the school diary is sufficient. For longer leaves, a formal application addressed to the Principal should be submitted in advance. In case of emergency or illness, parents can inform the class teacher via email or phone."},{question:"How can I meet my child's teacher?",answer:"Teachers are available for meetings during designated visiting hours, which are typically from 3:00 PM to 4:00 PM on working days. It is advisable to schedule an appointment through the school office or via email to ensure the teacher's availability."}];function f(){let[e,t]=(0,s.useState)("info"),[a,f]=(0,s.useState)({name:"",email:"",phone:"",childName:"",class:"",section:"",feedbackType:"",message:""}),[b,v]=(0,s.useState)(null),[j,y]=(0,s.useState)(!1),w=e=>{f({...a,[e.target.name]:e.target.value})},N=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,r.jsxs)("div",{className:"bg-white",children:[(0,r.jsx)("section",{className:"relative bg-gradient-to-br from-blue-50 via-white to-green-50",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8",children:(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",children:(0,r.jsx)("span",{className:"gradient-text",children:"Parent Zone"})}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:"Your dedicated space for school communications, updates, and resources to support your child's educational journey."})]})})}),(0,r.jsx)("section",{className:"bg-white border-b border-gray-200",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,r.jsx)("nav",{className:"flex space-x-8","aria-label":"Tabs",children:[{id:"info",name:"Information",icon:n},{id:"notices",name:"Notices",icon:i},{id:"circulars",name:"Circulars",icon:l.A},{id:"feedback",name:"Feedback",icon:o}].map(a=>(0,r.jsxs)("button",{onClick:()=>t(a.id),className:`${e===a.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm inline-flex items-center`,children:[(0,r.jsx)(a.icon,{className:"h-5 w-5 mr-2"}),a.name]},a.id))})})}),(0,r.jsx)("section",{className:"section-padding",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:["info"===e&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Parent Information"}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Essential information and resources for parents"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16",children:[(0,r.jsxs)("div",{className:"card p-6 text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Academic Calendar"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"View important dates, holidays, and events"}),(0,r.jsx)("button",{className:"btn-primary w-full",children:"View Calendar"})]}),(0,r.jsxs)("div",{className:"card p-6 text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(d.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Fee Payment"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Pay school fees online and view payment history"}),(0,r.jsx)("button",{className:"btn-primary w-full",children:"Pay Fees"})]}),(0,r.jsxs)("div",{className:"card p-6 text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-purple-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Student Progress"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Track your child's academic performance"}),(0,r.jsx)("button",{className:"btn-primary w-full",children:"View Progress"})]}),(0,r.jsxs)("div",{className:"card p-6 text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-yellow-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Parent Portal"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Access all parent services in one place"}),(0,r.jsx)("button",{className:"btn-primary w-full",children:"Login to Portal"})]})]}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-8 text-center",children:"Frequently Asked Questions"}),(0,r.jsx)("div",{className:"space-y-4",children:g.map((e,t)=>(0,r.jsxs)("div",{className:"card overflow-hidden",children:[(0,r.jsxs)("button",{className:"w-full px-6 py-4 text-left flex items-center justify-between",onClick:()=>v(b===t?null:t),children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.question}),(0,r.jsx)("span",{className:`transition-transform duration-200 ${b===t?"rotate-180":""}`,children:(0,r.jsx)("svg",{className:"h-5 w-5 text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),b===t&&(0,r.jsx)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200",children:(0,r.jsx)("p",{className:"text-gray-600",children:e.answer})})]},t))})]})]}),"notices"===e&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"School Notices"}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Important announcements and updates for parents"})]}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto space-y-6",children:x.map(e=>(0,r.jsx)("div",{className:`card p-6 border-l-4 ${"high"===e.priority?"border-l-red-500 bg-red-50":"medium"===e.priority?"border-l-yellow-500 bg-yellow-50":"border-l-green-500 bg-green-50"}`,children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-1"}),N(e.date)]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{className:"font-medium",children:e.category})]}),(0,r.jsx)("p",{className:"text-gray-600",children:e.content})]}),(0,r.jsx)("div",{className:"ml-4",children:(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"high"===e.priority?"bg-red-100 text-red-800":"medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:[e.priority.charAt(0).toUpperCase()+e.priority.slice(1)," Priority"]})})]})},e.id))})]}),"circulars"===e&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Official Circulars"}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Formal communications and policy updates from the school administration"})]}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto space-y-6",children:p.map(e=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mt-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-1"}),N(e.date)]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:["Ref: ",e.reference]})]})]})}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("p",{className:"text-gray-600",children:e.content})}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Download ",e.attachmentName]})})]},e.id))})]}),"feedback"===e&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Parent Feedback"}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"We value your input to help us improve our services and educational quality"})]}),(0,r.jsx)("div",{className:"max-w-3xl mx-auto",children:j?(0,r.jsxs)("div",{className:"card p-8 text-center",children:[(0,r.jsx)(h.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"Thank You!"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Your feedback has been submitted successfully. We appreciate your input and will review it carefully."})]}):(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),y(!0),setTimeout(()=>{f({name:"",email:"",phone:"",childName:"",class:"",section:"",feedbackType:"",message:""}),y(!1)},3e3)},className:"card p-8 space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name *"}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:a.name,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",required:!0,value:a.email,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,r.jsx)("input",{type:"tel",id:"phone",name:"phone",value:a.phone,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"childName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Child's Name *"}),(0,r.jsx)("input",{type:"text",id:"childName",name:"childName",required:!0,value:a.childName,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"class",className:"block text-sm font-medium text-gray-700 mb-2",children:"Class *"}),(0,r.jsxs)("select",{id:"class",name:"class",required:!0,value:a.class,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select Class"}),(0,r.jsx)("option",{value:"Nursery",children:"Nursery"}),(0,r.jsx)("option",{value:"LKG",children:"LKG"}),(0,r.jsx)("option",{value:"UKG",children:"UKG"}),(0,r.jsx)("option",{value:"I",children:"I"}),(0,r.jsx)("option",{value:"II",children:"II"}),(0,r.jsx)("option",{value:"III",children:"III"}),(0,r.jsx)("option",{value:"IV",children:"IV"}),(0,r.jsx)("option",{value:"V",children:"V"}),(0,r.jsx)("option",{value:"VI",children:"VI"}),(0,r.jsx)("option",{value:"VII",children:"VII"}),(0,r.jsx)("option",{value:"VIII",children:"VIII"}),(0,r.jsx)("option",{value:"IX",children:"IX"}),(0,r.jsx)("option",{value:"X",children:"X"}),(0,r.jsx)("option",{value:"XI",children:"XI"}),(0,r.jsx)("option",{value:"XII",children:"XII"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"section",className:"block text-sm font-medium text-gray-700 mb-2",children:"Section"}),(0,r.jsxs)("select",{id:"section",name:"section",value:a.section,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select Section"}),(0,r.jsx)("option",{value:"A",children:"A"}),(0,r.jsx)("option",{value:"B",children:"B"}),(0,r.jsx)("option",{value:"C",children:"C"}),(0,r.jsx)("option",{value:"D",children:"D"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"feedbackType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Feedback Type *"}),(0,r.jsxs)("select",{id:"feedbackType",name:"feedbackType",required:!0,value:a.feedbackType,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select Type"}),(0,r.jsx)("option",{value:"Academic",children:"Academic"}),(0,r.jsx)("option",{value:"Infrastructure",children:"Infrastructure"}),(0,r.jsx)("option",{value:"Administration",children:"Administration"}),(0,r.jsx)("option",{value:"Transportation",children:"Transportation"}),(0,r.jsx)("option",{value:"Extracurricular",children:"Extracurricular"}),(0,r.jsx)("option",{value:"Other",children:"Other"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Feedback *"}),(0,r.jsx)("textarea",{id:"message",name:"message",required:!0,rows:5,value:a.message,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Please share your feedback, suggestions, or concerns..."})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary px-8 py-3 text-lg",children:"Submit Feedback"})})]})})]})]})})]})}},5428:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(3210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))})},6016:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(3210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},6103:(e,t,a)=>{Promise.resolve().then(a.bind(a,4534))},8089:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(3210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},8445:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(3210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 8.25H9m6 3H9m3 6-3-3h1.5a3 3 0 1 0 0-6M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9982:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\parent-zone\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,727,658,158],()=>a(3634));module.exports=r})();