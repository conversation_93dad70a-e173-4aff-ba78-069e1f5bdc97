/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/parent-zone/page";
exports.ids = ["app/parent-zone/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fparent-zone%2Fpage&page=%2Fparent-zone%2Fpage&appPaths=%2Fparent-zone%2Fpage&pagePath=private-next-app-dir%2Fparent-zone%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fparent-zone%2Fpage&page=%2Fparent-zone%2Fpage&appPaths=%2Fparent-zone%2Fpage&pagePath=private-next-app-dir%2Fparent-zone%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/parent-zone/page.tsx */ \"(rsc)/./src/app/parent-zone/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'parent-zone',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/parent-zone/page\",\n        pathname: \"/parent-zone\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fparent-zone%2Fpage&page=%2Fparent-zone%2Fpage&appPaths=%2Fparent-zone%2Fpage&pagePath=private-next-app-dir%2Fparent-zone%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(rsc)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cparent-zone%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cparent-zone%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/parent-zone/page.tsx */ \"(rsc)/./src/app/parent-zone/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhcmVudC16b25lJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUFtSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU1RVRFlcXFxcUHJvamVjdHNcXFxcU2Nob29sXFxcXHNhcmFzdmF0aS1zY2hvb2xcXFxcc3JjXFxcXGFwcFxcXFxwYXJlbnQtem9uZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cparent-zone%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxTVFVEWVxcUHJvamVjdHNcXFNjaG9vbFxcc2FyYXN2YXRpLXNjaG9vbFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"56fc54f1c22b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcU1RVRFlcXFByb2plY3RzXFxTY2hvb2xcXHNhcmFzdmF0aS1zY2hvb2xcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjU2ZmM1NGYxYzIyYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Sarasvati School - Excellence in Education\",\n    description: \"Sarasvati School provides quality education from Pre-Primary to Class 12 with CBSE curriculum, fostering academic excellence and holistic development.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/parent-zone/page.tsx":
/*!**************************************!*\
  !*** ./src/app/parent-zone/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\parent-zone\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst navigation = {\n    main: [\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Academics',\n            href: '/academics'\n        },\n        {\n            name: 'Admissions',\n            href: '/admissions'\n        },\n        {\n            name: 'Faculty',\n            href: '/faculty'\n        },\n        {\n            name: 'Gallery',\n            href: '/gallery'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ],\n    student: [\n        {\n            name: 'Student Corner',\n            href: '/student-corner'\n        },\n        {\n            name: 'Timetables',\n            href: '/student-corner#timetables'\n        },\n        {\n            name: 'Syllabus',\n            href: '/student-corner#syllabus'\n        },\n        {\n            name: 'E-Learning',\n            href: '/student-corner#elearning'\n        }\n    ],\n    parent: [\n        {\n            name: 'Parent Zone',\n            href: '/parent-zone'\n        },\n        {\n            name: 'Notices',\n            href: '/parent-zone#notices'\n        },\n        {\n            name: 'Circulars',\n            href: '/parent-zone#circulars'\n        },\n        {\n            name: 'Feedback',\n            href: '/parent-zone#feedback'\n        }\n    ],\n    social: [\n        {\n            name: 'Facebook',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'Instagram',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.253 14.894 3.762 13.743 3.762 12.446s.49-2.448 1.364-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.608c-.807 0-1.297-.49-1.297-1.297s.49-1.297 1.297-1.297 1.297.49 1.297 1.297-.49 1.297-1.297 1.297zm-7.83 1.297c1.297 0 2.448.49 3.323 1.297.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.875-.875-1.297-2.026-1.297-3.323s.422-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'Twitter',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'YouTube',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ]\n};\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900\",\n        \"aria-labelledby\": \"footer-heading\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                id: \"footer-heading\",\n                className: \"sr-only\",\n                children: \"Footer\"\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:grid xl:grid-cols-3 xl:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Sarasvati School\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-300\",\n                                                        children: \"Excellence in Education\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm leading-6 text-gray-300\",\n                                        children: \"Nurturing young minds with quality education, values, and holistic development from Pre-Primary to Class 12. Building tomorrow's leaders today.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6\",\n                                        children: navigation.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-2 md:gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Quick Links\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.main.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-10 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.student.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-2 md:gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Parents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.parent.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 140,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-10 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Contact Info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-6 space-y-4 text-sm text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"123 Education Street\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                        lineNumber: 150,\n                                                                        columnNumber: 42\n                                                                    }, this),\n                                                                    \"Knowledge City, KC 12345\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Phone: +91 98765 43210\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Email: <EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Office Hours: Mon-Fri 8:00 AM - 4:00 PM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 border-t border-gray-700 pt-8 sm:mt-20 lg:mt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:flex md:items-center md:justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-6 md:order-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/privacy\",\n                                            className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/terms\",\n                                            className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                            children: \"Terms of Service\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-8 text-xs leading-5 text-gray-400 md:order-1 md:mt-0\",\n                                    children: \"\\xa9 2024 Sarasvati School. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Header.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cparent-zone%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cparent-zone%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/parent-zone/page.tsx */ \"(ssr)/./src/app/parent-zone/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhcmVudC16b25lJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUFtSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU1RVRFlcXFxcUHJvamVjdHNcXFxcU2Nob29sXFxcXHNhcmFzdmF0aS1zY2hvb2xcXFxcc3JjXFxcXGFwcFxcXFxwYXJlbnQtem9uZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cparent-zone%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/parent-zone/page.tsx":
/*!**************************************!*\
  !*** ./src/app/parent-zone/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ParentZonePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellAlertIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyRupeeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BellAlertIcon,CalendarDaysIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,InformationCircleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst notices = [\n    {\n        id: 1,\n        title: \"Fee Payment Reminder\",\n        date: \"2024-08-05\",\n        content: \"This is a reminder that the second quarter fees are due by August 15, 2024. Please ensure timely payment to avoid late fees. Payments can be made online through the school portal or at the accounts office during working hours.\",\n        category: \"Fees\",\n        priority: \"high\"\n    },\n    {\n        id: 2,\n        title: \"Parent-Teacher Meeting Schedule\",\n        date: \"2024-08-10\",\n        content: \"The Parent-Teacher Meeting for Classes I-V will be held on August 30, 2024, from 10:00 AM to 2:00 PM. Parents are requested to meet their child&apos;s class teacher as per the time slots that will be shared via email.\",\n        category: \"Meeting\",\n        priority: \"medium\"\n    },\n    {\n        id: 3,\n        title: \"School Uniform Update\",\n        date: \"2024-08-12\",\n        content: \"The school uniform shop will remain open on all Saturdays of August from 9:00 AM to 1:00 PM for parents to purchase new uniforms or replacements. Winter uniforms will be available from September 15, 2024.\",\n        category: \"General\",\n        priority: \"low\"\n    },\n    {\n        id: 4,\n        title: \"Annual Health Check-up\",\n        date: \"2024-08-15\",\n        content: \"The annual health check-up for all students will be conducted from August 25-30, 2024. Please ensure your child is present on the designated day. The schedule for each class will be shared via email.\",\n        category: \"Health\",\n        priority: \"medium\"\n    }\n];\nconst circulars = [\n    {\n        id: 1,\n        title: \"Revised School Timings\",\n        date: \"2024-08-01\",\n        reference: \"SSPS/2024-25/C-001\",\n        content: \"This is to inform all parents that the school timings will be revised from August 15, 2024. The new timings will be 8:30 AM to 3:00 PM for all classes. This change is being implemented to optimize the learning hours and reduce traffic congestion during arrival and departure times.\",\n        attachmentName: \"Revised_Timings_Circular.pdf\"\n    },\n    {\n        id: 2,\n        title: \"Annual Day Celebration\",\n        date: \"2024-08-08\",\n        reference: \"SSPS/2024-25/C-002\",\n        content: \"We are pleased to announce that the Annual Day celebration will be held on September 25, 2024, at the school auditorium. Students participating in the cultural programs will have practice sessions after school hours. Detailed information regarding the schedule and arrangements will be shared soon.\",\n        attachmentName: \"Annual_Day_Circular.pdf\"\n    },\n    {\n        id: 3,\n        title: \"Introduction of New Extracurricular Activities\",\n        date: \"2024-08-12\",\n        reference: \"SSPS/2024-25/C-003\",\n        content: \"The school is introducing new extracurricular activities for the academic year 2024-25, including Robotics, Western Dance, and Public Speaking. Interested students can register for these activities by filling out the attached form and submitting it to their class teacher by August 20, 2024.\",\n        attachmentName: \"New_Activities_Form.pdf\"\n    }\n];\nconst faqItems = [\n    {\n        question: \"How can I check my child&apos;s academic progress?\",\n        answer: \"You can check your child&apos;s academic progress through the Parent Portal. Regular updates on assessments, test scores, and teacher comments are available. Additionally, report cards are issued at the end of each term, and parent-teacher meetings are scheduled periodically.\"\n    },\n    {\n        question: \"What is the school's policy on attendance?\",\n        answer: \"Students are expected to maintain at least 85% attendance throughout the academic year. In case of absence, parents should inform the class teacher through a written note or email. For extended absences due to medical reasons, a doctor's certificate should be submitted.\"\n    },\n    {\n        question: \"How can I pay the school fees?\",\n        answer: \"School fees can be paid online through the Parent Portal using credit/debit cards or net banking. Alternatively, you can pay at the school accounts office by cash, cheque, or demand draft during working hours (9:00 AM to 2:00 PM on weekdays).\"\n    },\n    {\n        question: \"What is the procedure for applying for leave?\",\n        answer: \"For short leaves (1-2 days), a written note in the school diary is sufficient. For longer leaves, a formal application addressed to the Principal should be submitted in advance. In case of emergency or illness, parents can inform the class teacher via email or phone.\"\n    },\n    {\n        question: \"How can I meet my child&apos;s teacher?\",\n        answer: \"Teachers are available for meetings during designated visiting hours, which are typically from 3:00 PM to 4:00 PM on working days. It is advisable to schedule an appointment through the school office or via email to ensure the teacher's availability.\"\n    }\n];\nfunction ParentZonePage() {\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('info');\n    const [feedbackForm, setFeedbackForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        phone: '',\n        childName: '',\n        class: '',\n        section: '',\n        feedbackType: '',\n        message: ''\n    });\n    const [expandedFaq, setExpandedFaq] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formSubmitted, setFormSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (e)=>{\n        setFeedbackForm({\n            ...feedbackForm,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        // Handle form submission\n        setFormSubmitted(true);\n        // Reset form after submission\n        setTimeout(()=>{\n            setFeedbackForm({\n                name: '',\n                email: '',\n                phone: '',\n                childName: '',\n                class: '',\n                section: '',\n                feedbackType: '',\n                message: ''\n            });\n            setFormSubmitted(false);\n        }, 3000);\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-blue-50 via-white to-green-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Parent Zone\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-6 text-lg leading-8 text-gray-600\",\n                                children: \"Your dedicated space for school communications, updates, and resources to support your child's educational journey.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        \"aria-label\": \"Tabs\",\n                        children: [\n                            {\n                                id: 'info',\n                                name: 'Information',\n                                icon: _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n                            },\n                            {\n                                id: 'notices',\n                                name: 'Notices',\n                                icon: _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                            },\n                            {\n                                id: 'circulars',\n                                name: 'Circulars',\n                                icon: _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                            },\n                            {\n                                id: 'feedback',\n                                name: 'Feedback',\n                                icon: _barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedTab(tab.id),\n                                className: `${selectedTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm inline-flex items-center`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this),\n                                    tab.name\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        selectedTab === 'info' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-2xl text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"Parent Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"Essential information and resources for parents\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-8 w-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                    children: \"Academic Calendar\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4\",\n                                                    children: \"View important dates, holidays, and events\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-primary w-full\",\n                                                    children: \"View Calendar\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-8 w-8 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                    children: \"Fee Payment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4\",\n                                                    children: \"Pay school fees online and view payment history\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-primary w-full\",\n                                                    children: \"Pay Fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mx-auto h-16 w-16 bg-purple-100 rounded-full flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-8 w-8 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                    children: \"Student Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4\",\n                                                    children: \"Track your child's academic performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-primary w-full\",\n                                                    children: \"View Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mx-auto h-16 w-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 text-yellow-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                    children: \"Parent Portal\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4\",\n                                                    children: \"Access all parent services in one place\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-primary w-full\",\n                                                    children: \"Login to Portal\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-semibold text-gray-900 mb-8 text-center\",\n                                            children: \"Frequently Asked Questions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: faqItems.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full px-6 py-4 text-left flex items-center justify-between\",\n                                                            onClick: ()=>setExpandedFaq(expandedFaq === index ? null : index),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-medium text-gray-900\",\n                                                                    children: faq.question\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `transition-transform duration-200 ${expandedFaq === index ? 'rotate-180' : ''}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"h-5 w-5 text-gray-500\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 9l-7 7-7-7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expandedFaq === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-6 py-4 bg-gray-50 border-t border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: faq.answer\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this),\n                        selectedTab === 'notices' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-2xl text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"School Notices\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"Important announcements and updates for parents\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto space-y-6\",\n                                    children: notices.map((notice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `card p-6 border-l-4 ${notice.priority === 'high' ? 'border-l-red-500 bg-red-50' : notice.priority === 'medium' ? 'border-l-yellow-500 bg-yellow-50' : 'border-l-green-500 bg-green-50'}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                children: notice.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 text-sm text-gray-600 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                                lineNumber: 304,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatDate(notice.date)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: notice.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: notice.content\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${notice.priority === 'high' ? 'bg-red-100 text-red-800' : notice.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`,\n                                                            children: [\n                                                                notice.priority.charAt(0).toUpperCase() + notice.priority.slice(1),\n                                                                \" Priority\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, notice.id, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        selectedTab === 'circulars' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-2xl text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"Official Circulars\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"Formal communications and policy updates from the school administration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto space-y-6\",\n                                    children: circulars.map((circular)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: circular.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 text-sm text-gray-600 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatDate(circular.date)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Ref: \",\n                                                                            circular.reference\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: circular.content\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Download \",\n                                                            circular.attachmentName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, circular.id, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this),\n                        selectedTab === 'feedback' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-2xl text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"Parent Feedback\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"We value your input to help us improve our services and educational quality\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-3xl mx-auto\",\n                                    children: formSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BellAlertIcon_CalendarDaysIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_InformationCircleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mb-2\",\n                                                children: \"Thank You!\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Your feedback has been submitted successfully. We appreciate your input and will review it carefully.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"card p-8 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"name\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Your Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"name\",\n                                                                name: \"name\",\n                                                                required: true,\n                                                                value: feedbackForm.name,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"email\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Email Address *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                id: \"email\",\n                                                                name: \"email\",\n                                                                required: true,\n                                                                value: feedbackForm.email,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"phone\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Phone Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"tel\",\n                                                                id: \"phone\",\n                                                                name: \"phone\",\n                                                                value: feedbackForm.phone,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"childName\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Child's Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"childName\",\n                                                                name: \"childName\",\n                                                                required: true,\n                                                                value: feedbackForm.childName,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"class\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Class *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"class\",\n                                                                name: \"class\",\n                                                                required: true,\n                                                                value: feedbackForm.class,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Select Class\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Nursery\",\n                                                                        children: \"Nursery\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"LKG\",\n                                                                        children: \"LKG\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"UKG\",\n                                                                        children: \"UKG\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"I\",\n                                                                        children: \"I\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"II\",\n                                                                        children: \"II\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"III\",\n                                                                        children: \"III\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"IV\",\n                                                                        children: \"IV\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"V\",\n                                                                        children: \"V\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"VI\",\n                                                                        children: \"VI\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"VII\",\n                                                                        children: \"VII\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"VIII\",\n                                                                        children: \"VIII\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"IX\",\n                                                                        children: \"IX\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"X\",\n                                                                        children: \"X\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"XI\",\n                                                                        children: \"XI\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 478,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"XII\",\n                                                                        children: \"XII\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"section\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Section\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"section\",\n                                                                name: \"section\",\n                                                                value: feedbackForm.section,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Select Section\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"A\",\n                                                                        children: \"A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"B\",\n                                                                        children: \"B\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"C\",\n                                                                        children: \"C\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"D\",\n                                                                        children: \"D\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"feedbackType\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Feedback Type *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"feedbackType\",\n                                                                name: \"feedbackType\",\n                                                                required: true,\n                                                                value: feedbackForm.feedbackType,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Select Type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Academic\",\n                                                                        children: \"Academic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 513,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Infrastructure\",\n                                                                        children: \"Infrastructure\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Administration\",\n                                                                        children: \"Administration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Transportation\",\n                                                                        children: \"Transportation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Extracurricular\",\n                                                                        children: \"Extracurricular\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Other\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"message\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Your Feedback *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"message\",\n                                                        name: \"message\",\n                                                        required: true,\n                                                        rows: 5,\n                                                        value: feedbackForm.message,\n                                                        onChange: handleInputChange,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                        placeholder: \"Please share your feedback, suggestions, or concerns...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"btn-primary px-8 py-3 text-lg\",\n                                                    children: \"Submit Feedback\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\parent-zone\\\\page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/parent-zone/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigation = [\n    {\n        name: 'Home',\n        href: '/'\n    },\n    {\n        name: 'About Us',\n        href: '/about'\n    },\n    {\n        name: 'Academics',\n        href: '/academics'\n    },\n    {\n        name: 'Activities',\n        href: '/activities'\n    },\n    {\n        name: 'Admissions',\n        href: '/admissions'\n    },\n    {\n        name: 'Faculty',\n        href: '/faculty'\n    },\n    {\n        name: 'Gallery',\n        href: '/gallery'\n    },\n    {\n        name: 'News & Events',\n        href: '/news-events'\n    },\n    {\n        name: 'Student Corner',\n        href: '/student-corner'\n    },\n    {\n        name: 'Parent Zone',\n        href: '/parent-zone'\n    },\n    {\n        name: 'Contact',\n        href: '/contact'\n    }\n];\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                \"aria-label\": \"Top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"-m-1.5 p-1.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Sarasvati School\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden sm:block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-gray-900\",\n                                                        children: \"Sarasvati School\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: \"Excellence in Education\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex lg:gap-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors duration-200\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/admissions\",\n                                    className: \"rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 transition-colors duration-200\",\n                                    children: \"Apply Now\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    className: \"rounded-md border border-gray-300 px-4 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50 transition-colors duration-200\",\n                                    children: \"Contact Us\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\",\n                                onClick: ()=>setMobileMenuOpen(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        className: \"-m-1.5 p-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Sarasvati School\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-bold\",\n                                                            children: \"S\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: \"Sarasvati School\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"-m-2.5 rounded-md p-2.5 text-gray-700\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close menu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-6 w-6\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flow-root\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"-my-6 divide-y divide-gray-500/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 py-6\",\n                                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: item.href,\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: item.name\n                                                }, item.name, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/admissions\",\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-white bg-blue-600 hover:bg-blue-500\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Apply Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 border border-gray-300 hover:bg-gray-50\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fparent-zone%2Fpage&page=%2Fparent-zone%2Fpage&appPaths=%2Fparent-zone%2Fpage&pagePath=private-next-app-dir%2Fparent-zone%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();