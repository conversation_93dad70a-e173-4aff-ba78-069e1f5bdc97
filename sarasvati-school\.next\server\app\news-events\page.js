/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/news-events/page";
exports.ids = ["app/news-events/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fnews-events%2Fpage&page=%2Fnews-events%2Fpage&appPaths=%2Fnews-events%2Fpage&pagePath=private-next-app-dir%2Fnews-events%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fnews-events%2Fpage&page=%2Fnews-events%2Fpage&appPaths=%2Fnews-events%2Fpage&pagePath=private-next-app-dir%2Fnews-events%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/news-events/page.tsx */ \"(rsc)/./src/app/news-events/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'news-events',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/news-events/page\",\n        pathname: \"/news-events\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fnews-events%2Fpage&page=%2Fnews-events%2Fpage&appPaths=%2Fnews-events%2Fpage&pagePath=private-next-app-dir%2Fnews-events%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(rsc)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cnews-events%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cnews-events%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/news-events/page.tsx */ \"(rsc)/./src/app/news-events/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q25ld3MtZXZlbnRzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUFtSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU1RVRFlcXFxcUHJvamVjdHNcXFxcU2Nob29sXFxcXHNhcmFzdmF0aS1zY2hvb2xcXFxcc3JjXFxcXGFwcFxcXFxuZXdzLWV2ZW50c1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cnews-events%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxTVFVEWVxcUHJvamVjdHNcXFNjaG9vbFxcc2FyYXN2YXRpLXNjaG9vbFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"56fc54f1c22b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcU1RVRFlcXFByb2plY3RzXFxTY2hvb2xcXHNhcmFzdmF0aS1zY2hvb2xcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjU2ZmM1NGYxYzIyYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Sarasvati School - Excellence in Education\",\n    description: \"Sarasvati School provides quality education from Pre-Primary to Class 12 with CBSE curriculum, fostering academic excellence and holistic development.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/news-events/page.tsx":
/*!**************************************!*\
  !*** ./src/app/news-events/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\news-events\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst navigation = {\n    main: [\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Academics',\n            href: '/academics'\n        },\n        {\n            name: 'Admissions',\n            href: '/admissions'\n        },\n        {\n            name: 'Faculty',\n            href: '/faculty'\n        },\n        {\n            name: 'Gallery',\n            href: '/gallery'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ],\n    student: [\n        {\n            name: 'Student Corner',\n            href: '/student-corner'\n        },\n        {\n            name: 'Timetables',\n            href: '/student-corner#timetables'\n        },\n        {\n            name: 'Syllabus',\n            href: '/student-corner#syllabus'\n        },\n        {\n            name: 'E-Learning',\n            href: '/student-corner#elearning'\n        }\n    ],\n    parent: [\n        {\n            name: 'Parent Zone',\n            href: '/parent-zone'\n        },\n        {\n            name: 'Notices',\n            href: '/parent-zone#notices'\n        },\n        {\n            name: 'Circulars',\n            href: '/parent-zone#circulars'\n        },\n        {\n            name: 'Feedback',\n            href: '/parent-zone#feedback'\n        }\n    ],\n    social: [\n        {\n            name: 'Facebook',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'Instagram',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.253 14.894 3.762 13.743 3.762 12.446s.49-2.448 1.364-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.608c-.807 0-1.297-.49-1.297-1.297s.49-1.297 1.297-1.297 1.297.49 1.297 1.297-.49 1.297-1.297 1.297zm-7.83 1.297c1.297 0 2.448.49 3.323 1.297.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.875-.875-1.297-2.026-1.297-3.323s.422-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'Twitter',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'YouTube',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ]\n};\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900\",\n        \"aria-labelledby\": \"footer-heading\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                id: \"footer-heading\",\n                className: \"sr-only\",\n                children: \"Footer\"\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:grid xl:grid-cols-3 xl:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Sarasvati School\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-300\",\n                                                        children: \"Excellence in Education\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm leading-6 text-gray-300\",\n                                        children: \"Nurturing young minds with quality education, values, and holistic development from Pre-Primary to Class 12. Building tomorrow's leaders today.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6\",\n                                        children: navigation.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-2 md:gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Quick Links\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.main.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-10 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.student.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-2 md:gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Parents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.parent.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 140,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-10 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Contact Info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-6 space-y-4 text-sm text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"123 Education Street\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                        lineNumber: 150,\n                                                                        columnNumber: 42\n                                                                    }, this),\n                                                                    \"Knowledge City, KC 12345\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Phone: +91 98765 43210\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Email: <EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Office Hours: Mon-Fri 8:00 AM - 4:00 PM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 border-t border-gray-700 pt-8 sm:mt-20 lg:mt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:flex md:items-center md:justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-6 md:order-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/privacy\",\n                                            className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/terms\",\n                                            className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                            children: \"Terms of Service\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-8 text-xs leading-5 text-gray-400 md:order-1 md:mt-0\",\n                                    children: \"\\xa9 2024 Sarasvati School. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Header.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1NUVURZJTVDJTVDUHJvamVjdHMlNUMlNUNTY2hvb2wlNUMlNUNzYXJhc3ZhdGktc2Nob29sJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDU1RVRFklNUMlNUNQcm9qZWN0cyU1QyU1Q1NjaG9vbCU1QyU1Q3NhcmFzdmF0aS1zY2hvb2wlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1NUVURZJTVDJTVDUHJvamVjdHMlNUMlNUNTY2hvb2wlNUMlNUNzYXJhc3ZhdGktc2Nob29sJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQWdKO0FBQ2hKO0FBQ0EsME9BQW1KO0FBQ25KO0FBQ0EsME9BQW1KO0FBQ25KO0FBQ0Esb1JBQXlLO0FBQ3pLO0FBQ0Esd09BQWtKO0FBQ2xKO0FBQ0EsNFBBQTZKO0FBQzdKO0FBQ0Esa1FBQWdLO0FBQ2hLO0FBQ0Esc1FBQWlLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxTVFVEWVxcXFxQcm9qZWN0c1xcXFxTY2hvb2xcXFxcc2FyYXN2YXRpLXNjaG9vbFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxTVFVEWVxcXFxQcm9qZWN0c1xcXFxTY2hvb2xcXFxcc2FyYXN2YXRpLXNjaG9vbFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxTVFVEWVxcXFxQcm9qZWN0c1xcXFxTY2hvb2xcXFxcc2FyYXN2YXRpLXNjaG9vbFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxTVFVEWVxcXFxQcm9qZWN0c1xcXFxTY2hvb2xcXFxcc2FyYXN2YXRpLXNjaG9vbFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxTVFVEWVxcXFxQcm9qZWN0c1xcXFxTY2hvb2xcXFxcc2FyYXN2YXRpLXNjaG9vbFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFNUVURZXFxcXFByb2plY3RzXFxcXFNjaG9vbFxcXFxzYXJhc3ZhdGktc2Nob29sXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFNUVURZXFxcXFByb2plY3RzXFxcXFNjaG9vbFxcXFxzYXJhc3ZhdGktc2Nob29sXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFNUVURZXFxcXFByb2plY3RzXFxcXFNjaG9vbFxcXFxzYXJhc3ZhdGktc2Nob29sXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cnews-events%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cnews-events%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/news-events/page.tsx */ \"(ssr)/./src/app/news-events/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q25ld3MtZXZlbnRzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUFtSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcU1RVRFlcXFxcUHJvamVjdHNcXFxcU2Nob29sXFxcXHNhcmFzdmF0aS1zY2hvb2xcXFxcc3JjXFxcXGFwcFxcXFxuZXdzLWV2ZW50c1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cnews-events%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/news-events/page.tsx":
/*!**************************************!*\
  !*** ./src/app/news-events/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewsEventsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,CalendarDaysIcon,ClockIcon,MapPinIcon,MegaphoneIcon,MusicalNoteIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,CalendarDaysIcon,ClockIcon,MapPinIcon,MegaphoneIcon,MusicalNoteIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,CalendarDaysIcon,ClockIcon,MapPinIcon,MegaphoneIcon,MusicalNoteIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MusicalNoteIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,CalendarDaysIcon,ClockIcon,MapPinIcon,MegaphoneIcon,MusicalNoteIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,CalendarDaysIcon,ClockIcon,MapPinIcon,MegaphoneIcon,MusicalNoteIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,CalendarDaysIcon,ClockIcon,MapPinIcon,MegaphoneIcon,MusicalNoteIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MegaphoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,CalendarDaysIcon,ClockIcon,MapPinIcon,MegaphoneIcon,MusicalNoteIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,CalendarDaysIcon,ClockIcon,MapPinIcon,MegaphoneIcon,MusicalNoteIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst upcomingEvents = [\n    {\n        id: 1,\n        title: \"Annual Science Fair\",\n        date: \"2024-08-15\",\n        time: \"9:00 AM - 4:00 PM\",\n        location: \"School Auditorium\",\n        category: \"Academic\",\n        description: \"Students will showcase their innovative science projects and experiments.\",\n        icon: _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    },\n    {\n        id: 2,\n        title: \"Inter-House Sports Competition\",\n        date: \"2024-08-22\",\n        time: \"8:00 AM - 5:00 PM\",\n        location: \"School Sports Complex\",\n        category: \"Sports\",\n        description: \"Annual sports competition between all four houses.\",\n        icon: _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        id: 3,\n        title: \"Cultural Festival\",\n        date: \"2024-09-05\",\n        time: \"6:00 PM - 9:00 PM\",\n        location: \"Main Auditorium\",\n        category: \"Cultural\",\n        description: \"Celebration of arts, music, and cultural diversity.\",\n        icon: _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        id: 4,\n        title: \"Parent-Teacher Meeting\",\n        date: \"2024-08-30\",\n        time: \"10:00 AM - 2:00 PM\",\n        location: \"Respective Classrooms\",\n        category: \"Academic\",\n        description: \"Discussion about student progress and development.\",\n        icon: _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        id: 5,\n        title: \"Mathematics Olympiad\",\n        date: \"2024-09-12\",\n        time: \"10:00 AM - 12:00 PM\",\n        location: \"Examination Hall\",\n        category: \"Academic\",\n        description: \"National level mathematics competition for all grades.\",\n        icon: _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    }\n];\nconst announcements = [\n    {\n        id: 1,\n        title: \"New Academic Session 2024-25 Begins\",\n        date: \"2024-07-15\",\n        content: \"We are excited to welcome all students back for the new academic year. Classes will commence from July 20th, 2024.\",\n        priority: \"high\"\n    },\n    {\n        id: 2,\n        title: \"Library Renovation Complete\",\n        date: \"2024-07-10\",\n        content: \"Our library has been renovated with new furniture, additional study spaces, and upgraded digital resources.\",\n        priority: \"medium\"\n    },\n    {\n        id: 3,\n        title: \"Swimming Pool Maintenance\",\n        date: \"2024-07-08\",\n        content: \"The swimming pool will be closed for maintenance from July 15-20. Swimming classes will resume from July 21st.\",\n        priority: \"medium\"\n    },\n    {\n        id: 4,\n        title: \"New Bus Route Added\",\n        date: \"2024-07-05\",\n        content: \"We have added a new bus route covering the Green Valley area. Contact the transport office for details.\",\n        priority: \"low\"\n    },\n    {\n        id: 5,\n        title: \"Scholarship Applications Open\",\n        date: \"2024-07-01\",\n        content: \"Merit-based scholarship applications are now open for the academic year 2024-25. Last date to apply: July 31st.\",\n        priority: \"high\"\n    }\n];\nconst holidays = [\n    {\n        date: \"2024-08-15\",\n        name: \"Independence Day\",\n        type: \"National Holiday\"\n    },\n    {\n        date: \"2024-08-26\",\n        name: \"Janmashtami\",\n        type: \"Religious Holiday\"\n    },\n    {\n        date: \"2024-09-07\",\n        name: \"Ganesh Chaturthi\",\n        type: \"Religious Holiday\"\n    },\n    {\n        date: \"2024-10-02\",\n        name: \"Gandhi Jayanti\",\n        type: \"National Holiday\"\n    },\n    {\n        date: \"2024-10-12\",\n        name: \"Dussehra\",\n        type: \"Religious Holiday\"\n    },\n    {\n        date: \"2024-11-01\",\n        name: \"Diwali\",\n        type: \"Religious Holiday\"\n    },\n    {\n        date: \"2024-11-14\",\n        name: \"Children's Day\",\n        type: \"National Holiday\"\n    },\n    {\n        date: \"2024-12-25\",\n        name: \"Christmas\",\n        type: \"Religious Holiday\"\n    },\n    {\n        date: \"2025-01-26\",\n        name: \"Republic Day\",\n        type: \"National Holiday\"\n    },\n    {\n        date: \"2025-03-08\",\n        name: \"Holi\",\n        type: \"Religious Holiday\"\n    }\n];\nconst categoryColors = {\n    Academic: \"bg-blue-100 text-blue-800\",\n    Sports: \"bg-green-100 text-green-800\",\n    Cultural: \"bg-purple-100 text-purple-800\",\n    General: \"bg-gray-100 text-gray-800\"\n};\nconst priorityColors = {\n    high: \"border-l-red-500 bg-red-50\",\n    medium: \"border-l-yellow-500 bg-yellow-50\",\n    low: \"border-l-green-500 bg-green-50\"\n};\nfunction NewsEventsPage() {\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('events');\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-blue-50 via-white to-green-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"News & Events\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-6 text-lg leading-8 text-gray-600\",\n                                children: \"Stay updated with the latest happenings, upcoming events, and important announcements from Sarasvati School.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        \"aria-label\": \"Tabs\",\n                        children: [\n                            {\n                                id: 'events',\n                                name: 'Upcoming Events',\n                                icon: _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                id: 'announcements',\n                                name: 'Announcements',\n                                icon: _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                            },\n                            {\n                                id: 'holidays',\n                                name: 'Holiday Calendar',\n                                icon: _barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedTab(tab.id),\n                                className: `${selectedTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm inline-flex items-center`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, this),\n                                    tab.name\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        selectedTab === 'events' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-2xl text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"Upcoming Events\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"Mark your calendars for these exciting upcoming events\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                    children: upcomingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(event.icon, {\n                                                                className: \"h-6 w-6 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                                        children: event.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${categoryColors[event.category]}`,\n                                                                        children: event.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 213,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                                lineNumber: 221,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatDate(event.date)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                                lineNumber: 225,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            event.time\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                                lineNumber: 229,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            event.location\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-3 text-gray-600\",\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, event.id, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this),\n                        selectedTab === 'announcements' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-2xl text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"School Announcements\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"Important updates and information for students and parents\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto space-y-6\",\n                                    children: announcements.map((announcement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `card p-6 border-l-4 ${priorityColors[announcement.priority]}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                children: announcement.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-3\",\n                                                                children: announcement.content\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_CalendarDaysIcon_ClockIcon_MapPinIcon_MegaphoneIcon_MusicalNoteIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    formatDate(announcement.date)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${announcement.priority === 'high' ? 'bg-red-100 text-red-800' : announcement.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`,\n                                                            children: [\n                                                                announcement.priority.charAt(0).toUpperCase() + announcement.priority.slice(1),\n                                                                \" Priority\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, announcement.id, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        selectedTab === 'holidays' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-2xl text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"Holiday Calendar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"Academic year 2024-25 holiday schedule\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-4 text-white font-semibold\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: \"Holiday\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: \"Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: holidays.map((holiday, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `px-6 py-4 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: formatDate(holiday.date)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: holiday.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${holiday.type === 'National Holiday' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}`,\n                                                                            children: holiday.type\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 p-4 bg-yellow-50 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-yellow-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Note:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Holiday dates are subject to change based on government notifications. Any changes will be communicated in advance through official school channels.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-blue-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight text-white sm:text-4xl\",\n                                children: \"Stay Updated\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-lg text-blue-100\",\n                                children: \"Subscribe to our newsletter to receive the latest news and updates directly in your inbox\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 flex max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        placeholder: \"Enter your email\",\n                                        className: \"flex-1 px-4 py-3 rounded-l-md border-0 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-6 py-3 bg-white text-blue-600 font-semibold rounded-r-md hover:bg-blue-50 transition-colors duration-200\",\n                                        children: \"Subscribe\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\news-events\\\\page.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/news-events/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigation = [\n    {\n        name: 'Home',\n        href: '/'\n    },\n    {\n        name: 'About Us',\n        href: '/about'\n    },\n    {\n        name: 'Academics',\n        href: '/academics'\n    },\n    {\n        name: 'Activities',\n        href: '/activities'\n    },\n    {\n        name: 'Admissions',\n        href: '/admissions'\n    },\n    {\n        name: 'Faculty',\n        href: '/faculty'\n    },\n    {\n        name: 'Gallery',\n        href: '/gallery'\n    },\n    {\n        name: 'News & Events',\n        href: '/news-events'\n    },\n    {\n        name: 'Student Corner',\n        href: '/student-corner'\n    },\n    {\n        name: 'Parent Zone',\n        href: '/parent-zone'\n    },\n    {\n        name: 'Contact',\n        href: '/contact'\n    }\n];\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                \"aria-label\": \"Top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"-m-1.5 p-1.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Sarasvati School\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden sm:block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-gray-900\",\n                                                        children: \"Sarasvati School\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: \"Excellence in Education\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex lg:gap-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors duration-200\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/admissions\",\n                                    className: \"rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 transition-colors duration-200\",\n                                    children: \"Apply Now\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    className: \"rounded-md border border-gray-300 px-4 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50 transition-colors duration-200\",\n                                    children: \"Contact Us\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\",\n                                onClick: ()=>setMobileMenuOpen(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        className: \"-m-1.5 p-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Sarasvati School\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-bold\",\n                                                            children: \"S\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: \"Sarasvati School\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"-m-2.5 rounded-md p-2.5 text-gray-700\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close menu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-6 w-6\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flow-root\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"-my-6 divide-y divide-gray-500/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 py-6\",\n                                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: item.href,\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: item.name\n                                                }, item.name, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/admissions\",\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-white bg-blue-600 hover:bg-blue-500\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Apply Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 border border-gray-300 hover:bg-gray-50\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fnews-events%2Fpage&page=%2Fnews-events%2Fpage&appPaths=%2Fnews-events%2Fpage&pagePath=private-next-app-dir%2Fnews-events%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();