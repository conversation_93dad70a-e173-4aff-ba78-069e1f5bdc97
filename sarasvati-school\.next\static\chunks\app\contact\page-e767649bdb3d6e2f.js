(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{2089:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(2115);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 8.25H9m6 3H9m3 6-3-3h1.5a3 3 0 1 0 0-6M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},2461:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(5155),r=t(2115),n=t(4648),l=t(2089);let i=r.forwardRef(function(e,s){let{title:t,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},n),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}),c=r.forwardRef(function(e,s){let{title:t,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},n),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))});var d=t(7208),o=t(4633),m=t(8593),x=t(2771),h=t(6865);let u={address:{street:"123 Education Street",area:"Knowledge City",city:"New Delhi",state:"Delhi",pincode:"110001",country:"India"},phone:{main:"+91 98765 43210",admissions:"+91 98765 43211",accounts:"+91 98765 43212",transport:"+91 98765 43213"},email:{general:"<EMAIL>",admissions:"<EMAIL>",principal:"<EMAIL>",accounts:"<EMAIL>"}},p=[{name:"Admissions Office",icon:n.A,phone:u.phone.admissions,email:u.email.admissions,description:"For new admissions, application process, and enrollment queries"},{name:"Accounts Department",icon:l.A,phone:u.phone.accounts,email:u.email.accounts,description:"For fee payments, financial queries, and account-related matters"},{name:"Principal's Office",icon:i,phone:u.phone.main,email:u.email.principal,description:"For academic matters, complaints, and administrative issues"},{name:"Transport Department",icon:c,phone:u.phone.transport,email:"<EMAIL>",description:"For school bus routes, transport fees, and related services"}];function g(){let[e,s]=(0,r.useState)({name:"",email:"",phone:"",subject:"",department:"",message:""}),[t,n]=(0,r.useState)(!1),l=t=>{s({...e,[t.target.name]:t.target.value})};return(0,a.jsxs)("div",{className:"bg-white",children:[(0,a.jsx)("section",{className:"relative bg-gradient-to-br from-blue-50 via-white to-green-50",children:(0,a.jsx)("div",{className:"mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8",children:(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",children:(0,a.jsx)("span",{className:"gradient-text",children:"Contact Us"})}),(0,a.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:"Get in touch with us for any queries, admissions, or support. We're here to help you and your child succeed."})]})})}),(0,a.jsx)("section",{className:"section-padding",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Get in Touch"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Multiple ways to reach us for your convenience"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16",children:[(0,a.jsxs)("div",{className:"card p-8 text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)(d.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Visit Us"}),(0,a.jsxs)("div",{className:"text-gray-600 space-y-1",children:[(0,a.jsx)("p",{children:u.address.street}),(0,a.jsx)("p",{children:u.address.area}),(0,a.jsxs)("p",{children:[u.address.city,", ",u.address.state]}),(0,a.jsxs)("p",{children:[u.address.pincode,", ",u.address.country]})]})]}),(0,a.jsxs)("div",{className:"card p-8 text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)(o.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Call Us"}),(0,a.jsxs)("div",{className:"text-gray-600 space-y-2",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Main Office:"}),(0,a.jsx)("br",{}),u.phone.main]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Admissions:"}),(0,a.jsx)("br",{}),u.phone.admissions]})]})]}),(0,a.jsxs)("div",{className:"card p-8 text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-purple-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Email Us"}),(0,a.jsxs)("div",{className:"text-gray-600 space-y-2",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"General:"}),(0,a.jsx)("br",{}),u.email.general]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Admissions:"}),(0,a.jsx)("br",{}),u.email.admissions]})]})]})]}),(0,a.jsxs)("div",{className:"card p-8 text-center mb-16",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-yellow-600"})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Office Hours"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 mb-2",children:"Weekdays"}),(0,a.jsx)("p",{children:"Monday - Friday"}),(0,a.jsx)("p",{children:"8:00 AM - 4:00 PM"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 mb-2",children:"Saturday"}),(0,a.jsx)("p",{children:"8:00 AM - 1:00 PM"}),(0,a.jsx)("p",{children:"(Limited Services)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 mb-2",children:"Sunday"}),(0,a.jsx)("p",{children:"Closed"}),(0,a.jsx)("p",{children:"(Emergency Contact Available)"})]})]})]})]})}),(0,a.jsx)("section",{className:"section-padding bg-gray-50",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Department Contacts"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Direct contact information for specific departments"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:p.map((e,s)=>(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(e.icon,{className:"h-6 w-6 text-blue-600"})})}),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),e.phone]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),e.email]})]})]})]})},s))})]})}),(0,a.jsx)("section",{className:"section-padding",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Send us a Message"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Fill out the form below and we'll get back to you as soon as possible"})]}),(0,a.jsx)("div",{className:"max-w-3xl mx-auto",children:t?(0,a.jsxs)("div",{className:"card p-8 text-center",children:[(0,a.jsx)(h.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"Message Sent!"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Thank you for contacting us. We'll get back to you within 24 hours."})]}):(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n(!0),setTimeout(()=>{s({name:"",email:"",phone:"",subject:"",department:"",message:""}),n(!1)},3e3)},className:"card p-8 space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:e.name,onChange:l,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",required:!0,value:e.email,onChange:l,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:l,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"department",className:"block text-sm font-medium text-gray-700 mb-2",children:"Department"}),(0,a.jsxs)("select",{id:"department",name:"department",value:e.department,onChange:l,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Department"}),(0,a.jsx)("option",{value:"admissions",children:"Admissions"}),(0,a.jsx)("option",{value:"academics",children:"Academics"}),(0,a.jsx)("option",{value:"accounts",children:"Accounts"}),(0,a.jsx)("option",{value:"transport",children:"Transport"}),(0,a.jsx)("option",{value:"administration",children:"Administration"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject *"}),(0,a.jsx)("input",{type:"text",id:"subject",name:"subject",required:!0,value:e.subject,onChange:l,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),(0,a.jsx)("textarea",{id:"message",name:"message",required:!0,rows:5,value:e.message,onChange:l,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Please describe your query or message..."})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{type:"submit",className:"btn-primary px-8 py-3 text-lg",children:"Send Message"})})]})})]})}),(0,a.jsx)("section",{className:"section-padding bg-gray-50",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Find Us on Map"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Located in the heart of the educational district"})]}),(0,a.jsx)("div",{className:"card overflow-hidden",children:(0,a.jsx)("div",{className:"aspect-w-16 aspect-h-9 bg-gray-200",children:(0,a.jsx)("div",{className:"w-full h-96 bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Interactive Google Maps would be embedded here"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"123 Education Street, Knowledge City, New Delhi - 110001"})]})})})}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)("a",{href:"https://maps.google.com",target:"_blank",rel:"noopener noreferrer",className:"btn-primary inline-flex items-center",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"Open in Google Maps"]})})]})})]})}},2771:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(2115);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},4633:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(2115);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))})},4648:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(2115);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))})},5196:(e,s,t)=>{Promise.resolve().then(t.bind(t,2461))},6865:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(2115);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},7208:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(2115);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))})},8593:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(2115);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(5196)),_N_E=e.O()}]);