[{"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\about\\page.tsx": "1", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\academics\\page.tsx": "2", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\activities\\page.tsx": "3", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\admissions\\page.tsx": "4", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\contact\\page.tsx": "5", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\faculty\\page.tsx": "6", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\gallery\\page.tsx": "7", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\layout.tsx": "8", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\news-events\\page.tsx": "9", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\page.tsx": "10", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\parent-zone\\page.tsx": "11", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\student-corner\\page.tsx": "12", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Footer.tsx": "13", "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Header.tsx": "14"}, {"size": 14485, "mtime": 1752157428209, "results": "15", "hashOfConfig": "16"}, {"size": 18392, "mtime": 1752156510092, "results": "17", "hashOfConfig": "16"}, {"size": 17051, "mtime": 1752156597582, "results": "18", "hashOfConfig": "16"}, {"size": 23238, "mtime": 1752156693241, "results": "19", "hashOfConfig": "16"}, {"size": 16562, "mtime": 1752157165909, "results": "20", "hashOfConfig": "16"}, {"size": 13461, "mtime": 1752156761442, "results": "21", "hashOfConfig": "16"}, {"size": 12980, "mtime": 1752156827105, "results": "22", "hashOfConfig": "16"}, {"size": 950, "mtime": 1752155444136, "results": "23", "hashOfConfig": "16"}, {"size": 14547, "mtime": 1752156892050, "results": "24", "hashOfConfig": "16"}, {"size": 9233, "mtime": 1752155693264, "results": "25", "hashOfConfig": "16"}, {"size": 26521, "mtime": 1752157093684, "results": "26", "hashOfConfig": "16"}, {"size": 20692, "mtime": 1752156977060, "results": "27", "hashOfConfig": "16"}, {"size": 8334, "mtime": 1752155514176, "results": "28", "hashOfConfig": "16"}, {"size": 6139, "mtime": 1752155474445, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9h5s2m", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\about\\page.tsx", [], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\academics\\page.tsx", ["72", "73", "74", "75"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\activities\\page.tsx", [], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\admissions\\page.tsx", ["76", "77", "78", "79"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\contact\\page.tsx", ["80", "81", "82"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\faculty\\page.tsx", ["83", "84"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\gallery\\page.tsx", ["85"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\layout.tsx", [], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\news-events\\page.tsx", [], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\page.tsx", ["86", "87", "88", "89"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\parent-zone\\page.tsx", ["90", "91", "92", "93", "94"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\student-corner\\page.tsx", ["95"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Footer.tsx", ["96", "97", "98", "99", "100"], [], "D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Header.tsx", [], [], {"ruleId": "101", "severity": 2, "message": "102", "line": 5, "column": 3, "nodeType": null, "messageId": "103", "endLine": 5, "endColumn": 16}, {"ruleId": "101", "severity": 2, "message": "104", "line": 10, "column": 3, "nodeType": null, "messageId": "103", "endLine": 10, "endColumn": 15}, {"ruleId": "101", "severity": 2, "message": "105", "line": 11, "column": 3, "nodeType": null, "messageId": "103", "endLine": 11, "endColumn": 17}, {"ruleId": "101", "severity": 2, "message": "106", "line": 63, "column": 39, "nodeType": null, "messageId": "103", "endLine": 63, "endColumn": 44}, {"ruleId": "101", "severity": 2, "message": "107", "line": 5, "column": 3, "nodeType": null, "messageId": "103", "endLine": 5, "endColumn": 28}, {"ruleId": "101", "severity": 2, "message": "108", "line": 9, "column": 3, "nodeType": null, "messageId": "103", "endLine": 9, "endColumn": 11}, {"ruleId": "109", "severity": 2, "message": "110", "line": 290, "column": 27, "nodeType": "111", "messageId": "112", "suggestions": "113"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 304, "column": 27, "nodeType": "111", "messageId": "112", "suggestions": "114"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 122, "column": 17, "nodeType": "111", "messageId": "112", "suggestions": "115"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 256, "column": 45, "nodeType": "111", "messageId": "112", "suggestions": "116"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 265, "column": 50, "nodeType": "111", "messageId": "112", "suggestions": "117"}, {"ruleId": "101", "severity": 2, "message": "118", "line": 8, "column": 3, "nodeType": null, "messageId": "103", "endLine": 8, "endColumn": 18}, {"ruleId": "109", "severity": 2, "message": "110", "line": 266, "column": 53, "nodeType": "111", "messageId": "112", "suggestions": "119"}, {"ruleId": "101", "severity": 2, "message": "120", "line": 4, "column": 8, "nodeType": null, "messageId": "103", "endLine": 4, "endColumn": 13}, {"ruleId": "101", "severity": 2, "message": "120", "line": 2, "column": 8, "nodeType": null, "messageId": "103", "endLine": 2, "endColumn": 13}, {"ruleId": "109", "severity": 2, "message": "110", "line": 26, "column": 69, "nodeType": "111", "messageId": "112", "suggestions": "121"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 130, "column": 29, "nodeType": "111", "messageId": "112", "suggestions": "122"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 187, "column": 53, "nodeType": "111", "messageId": "112", "suggestions": "123"}, {"ruleId": "101", "severity": 2, "message": "124", "line": 13, "column": 3, "nodeType": null, "messageId": "103", "endLine": 13, "endColumn": 12}, {"ruleId": "101", "severity": 2, "message": "125", "line": 15, "column": 3, "nodeType": null, "messageId": "103", "endLine": 15, "endColumn": 24}, {"ruleId": "109", "severity": 2, "message": "110", "line": 165, "column": 36, "nodeType": "111", "messageId": "112", "suggestions": "126"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 235, "column": 77, "nodeType": "111", "messageId": "112", "suggestions": "127"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 437, "column": 32, "nodeType": "111", "messageId": "112", "suggestions": "128"}, {"ruleId": "101", "severity": 2, "message": "129", "line": 9, "column": 3, "nodeType": null, "messageId": "103", "endLine": 9, "endColumn": 18}, {"ruleId": "130", "severity": 2, "message": "131", "line": 28, "column": 21, "nodeType": "132", "messageId": "133", "endLine": 28, "endColumn": 24, "suggestions": "134"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 41, "column": 21, "nodeType": "132", "messageId": "133", "endLine": 41, "endColumn": 24, "suggestions": "135"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 54, "column": 21, "nodeType": "132", "messageId": "133", "endLine": 54, "endColumn": 24, "suggestions": "136"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 63, "column": 21, "nodeType": "132", "messageId": "133", "endLine": 63, "endColumn": 24, "suggestions": "137"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 96, "column": 62, "nodeType": "111", "messageId": "112", "suggestions": "138"}, "@typescript-eslint/no-unused-vars", "'UserGroupIcon' is defined but never used.", "unusedVar", "'LanguageIcon' is defined but never used.", "'PaintBrushIcon' is defined but never used.", "'index' is defined but never used.", "'ClipboardDocumentListIcon' is defined but never used.", "'UserIcon' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["139", "140", "141", "142"], ["143", "144", "145", "146"], ["147", "148", "149", "150"], ["151", "152", "153", "154"], ["155", "156", "157", "158"], "'MusicalNoteIcon' is defined but never used.", ["159", "160", "161", "162"], "'Image' is defined but never used.", ["163", "164", "165", "166"], ["167", "168", "169", "170"], ["171", "172", "173", "174"], "'ClockIcon' is defined but never used.", "'ExclamationCircleIcon' is defined but never used.", ["175", "176", "177", "178"], ["179", "180", "181", "182"], ["183", "184", "185", "186"], "'AcademicCapIcon' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["187", "188"], ["189", "190"], ["191", "192"], ["193", "194"], ["195", "196", "197", "198"], {"messageId": "199", "data": "200", "fix": "201", "desc": "202"}, {"messageId": "199", "data": "203", "fix": "204", "desc": "205"}, {"messageId": "199", "data": "206", "fix": "207", "desc": "208"}, {"messageId": "199", "data": "209", "fix": "210", "desc": "211"}, {"messageId": "199", "data": "212", "fix": "213", "desc": "202"}, {"messageId": "199", "data": "214", "fix": "215", "desc": "205"}, {"messageId": "199", "data": "216", "fix": "217", "desc": "208"}, {"messageId": "199", "data": "218", "fix": "219", "desc": "211"}, {"messageId": "199", "data": "220", "fix": "221", "desc": "202"}, {"messageId": "199", "data": "222", "fix": "223", "desc": "205"}, {"messageId": "199", "data": "224", "fix": "225", "desc": "208"}, {"messageId": "199", "data": "226", "fix": "227", "desc": "211"}, {"messageId": "199", "data": "228", "fix": "229", "desc": "202"}, {"messageId": "199", "data": "230", "fix": "231", "desc": "205"}, {"messageId": "199", "data": "232", "fix": "233", "desc": "208"}, {"messageId": "199", "data": "234", "fix": "235", "desc": "211"}, {"messageId": "199", "data": "236", "fix": "237", "desc": "202"}, {"messageId": "199", "data": "238", "fix": "239", "desc": "205"}, {"messageId": "199", "data": "240", "fix": "241", "desc": "208"}, {"messageId": "199", "data": "242", "fix": "243", "desc": "211"}, {"messageId": "199", "data": "244", "fix": "245", "desc": "202"}, {"messageId": "199", "data": "246", "fix": "247", "desc": "205"}, {"messageId": "199", "data": "248", "fix": "249", "desc": "208"}, {"messageId": "199", "data": "250", "fix": "251", "desc": "211"}, {"messageId": "199", "data": "252", "fix": "253", "desc": "202"}, {"messageId": "199", "data": "254", "fix": "255", "desc": "205"}, {"messageId": "199", "data": "256", "fix": "257", "desc": "208"}, {"messageId": "199", "data": "258", "fix": "259", "desc": "211"}, {"messageId": "199", "data": "260", "fix": "261", "desc": "202"}, {"messageId": "199", "data": "262", "fix": "263", "desc": "205"}, {"messageId": "199", "data": "264", "fix": "265", "desc": "208"}, {"messageId": "199", "data": "266", "fix": "267", "desc": "211"}, {"messageId": "199", "data": "268", "fix": "269", "desc": "202"}, {"messageId": "199", "data": "270", "fix": "271", "desc": "205"}, {"messageId": "199", "data": "272", "fix": "273", "desc": "208"}, {"messageId": "199", "data": "274", "fix": "275", "desc": "211"}, {"messageId": "199", "data": "276", "fix": "277", "desc": "202"}, {"messageId": "199", "data": "278", "fix": "279", "desc": "205"}, {"messageId": "199", "data": "280", "fix": "281", "desc": "208"}, {"messageId": "199", "data": "282", "fix": "283", "desc": "211"}, {"messageId": "199", "data": "284", "fix": "285", "desc": "202"}, {"messageId": "199", "data": "286", "fix": "287", "desc": "205"}, {"messageId": "199", "data": "288", "fix": "289", "desc": "208"}, {"messageId": "199", "data": "290", "fix": "291", "desc": "211"}, {"messageId": "199", "data": "292", "fix": "293", "desc": "202"}, {"messageId": "199", "data": "294", "fix": "295", "desc": "205"}, {"messageId": "199", "data": "296", "fix": "297", "desc": "208"}, {"messageId": "199", "data": "298", "fix": "299", "desc": "211"}, {"messageId": "300", "fix": "301", "desc": "302"}, {"messageId": "303", "fix": "304", "desc": "305"}, {"messageId": "300", "fix": "306", "desc": "302"}, {"messageId": "303", "fix": "307", "desc": "305"}, {"messageId": "300", "fix": "308", "desc": "302"}, {"messageId": "303", "fix": "309", "desc": "305"}, {"messageId": "300", "fix": "310", "desc": "302"}, {"messageId": "303", "fix": "311", "desc": "305"}, {"messageId": "199", "data": "312", "fix": "313", "desc": "202"}, {"messageId": "199", "data": "314", "fix": "315", "desc": "205"}, {"messageId": "199", "data": "316", "fix": "317", "desc": "208"}, {"messageId": "199", "data": "318", "fix": "319", "desc": "211"}, "replaceWithAlt", {"alt": "320"}, {"range": "321", "text": "322"}, "Replace with `&apos;`.", {"alt": "323"}, {"range": "324", "text": "325"}, "Replace with `&lsquo;`.", {"alt": "326"}, {"range": "327", "text": "328"}, "Replace with `&#39;`.", {"alt": "329"}, {"range": "330", "text": "331"}, "Replace with `&rsquo;`.", {"alt": "320"}, {"range": "332", "text": "333"}, {"alt": "323"}, {"range": "334", "text": "335"}, {"alt": "326"}, {"range": "336", "text": "337"}, {"alt": "329"}, {"range": "338", "text": "339"}, {"alt": "320"}, {"range": "340", "text": "341"}, {"alt": "323"}, {"range": "342", "text": "343"}, {"alt": "326"}, {"range": "344", "text": "345"}, {"alt": "329"}, {"range": "346", "text": "347"}, {"alt": "320"}, {"range": "348", "text": "349"}, {"alt": "323"}, {"range": "350", "text": "351"}, {"alt": "326"}, {"range": "352", "text": "353"}, {"alt": "329"}, {"range": "354", "text": "355"}, {"alt": "320"}, {"range": "356", "text": "357"}, {"alt": "323"}, {"range": "358", "text": "359"}, {"alt": "326"}, {"range": "360", "text": "361"}, {"alt": "329"}, {"range": "362", "text": "363"}, {"alt": "320"}, {"range": "364", "text": "365"}, {"alt": "323"}, {"range": "366", "text": "367"}, {"alt": "326"}, {"range": "368", "text": "369"}, {"alt": "329"}, {"range": "370", "text": "371"}, {"alt": "320"}, {"range": "372", "text": "373"}, {"alt": "323"}, {"range": "374", "text": "375"}, {"alt": "326"}, {"range": "376", "text": "377"}, {"alt": "329"}, {"range": "378", "text": "379"}, {"alt": "320"}, {"range": "380", "text": "381"}, {"alt": "323"}, {"range": "382", "text": "383"}, {"alt": "326"}, {"range": "384", "text": "385"}, {"alt": "329"}, {"range": "386", "text": "387"}, {"alt": "320"}, {"range": "388", "text": "389"}, {"alt": "323"}, {"range": "390", "text": "391"}, {"alt": "326"}, {"range": "392", "text": "393"}, {"alt": "329"}, {"range": "394", "text": "395"}, {"alt": "320"}, {"range": "396", "text": "397"}, {"alt": "323"}, {"range": "398", "text": "399"}, {"alt": "326"}, {"range": "400", "text": "401"}, {"alt": "329"}, {"range": "402", "text": "403"}, {"alt": "320"}, {"range": "404", "text": "405"}, {"alt": "323"}, {"range": "406", "text": "407"}, {"alt": "326"}, {"range": "408", "text": "409"}, {"alt": "329"}, {"range": "410", "text": "411"}, {"alt": "320"}, {"range": "412", "text": "413"}, {"alt": "323"}, {"range": "414", "text": "415"}, {"alt": "326"}, {"range": "416", "text": "417"}, {"alt": "329"}, {"range": "418", "text": "419"}, "suggestUnknown", {"range": "420", "text": "421"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "422", "text": "423"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "424", "text": "421"}, {"range": "425", "text": "423"}, {"range": "426", "text": "421"}, {"range": "427", "text": "423"}, {"range": "428", "text": "421"}, {"range": "429", "text": "423"}, {"alt": "320"}, {"range": "430", "text": "431"}, {"alt": "323"}, {"range": "432", "text": "433"}, {"alt": "326"}, {"range": "434", "text": "435"}, {"alt": "329"}, {"range": "436", "text": "437"}, "&apos;", [13175, 13230], "\n                    Father&apos;s Name *\n                  ", "&lsquo;", [13175, 13230], "\n                    Father&lsquo;s Name *\n                  ", "&#39;", [13175, 13230], "\n                    Father&#39;s Name *\n                  ", "&rsquo;", [13175, 13230], "\n                    Father&rsquo;s Name *\n                  ", [13800, 13855], "\n                    Mother&apos;s Name *\n                  ", [13800, 13855], "\n                    Mother&lsquo;s Name *\n                  ", [13800, 13855], "\n                    Mother&#39;s Name *\n                  ", [13800, 13855], "\n                    Mother&rsquo;s Name *\n                  ", [3242, 3393], "\n              Get in touch with us for any queries, admissions, or support. \n              We&apos;re here to help you and your child succeed.\n            ", [3242, 3393], "\n              Get in touch with us for any queries, admissions, or support. \n              We&lsquo;re here to help you and your child succeed.\n            ", [3242, 3393], "\n              Get in touch with us for any queries, admissions, or support. \n              We&#39;re here to help you and your child succeed.\n            ", [3242, 3393], "\n              Get in touch with us for any queries, admissions, or support. \n              We&rsquo;re here to help you and your child succeed.\n            ", [9439, 9536], "\n              Fill out the form below and we&apos;ll get back to you as soon as possible\n            ", [9439, 9536], "\n              Fill out the form below and we&lsquo;ll get back to you as soon as possible\n            ", [9439, 9536], "\n              Fill out the form below and we&#39;ll get back to you as soon as possible\n            ", [9439, 9536], "\n              Fill out the form below and we&rsquo;ll get back to you as soon as possible\n            ", [9912, 10015], "\n                  Thank you for contacting us. We&apos;ll get back to you within 24 hours.\n                ", [9912, 10015], "\n                  Thank you for contacting us. We&lsquo;ll get back to you within 24 hours.\n                ", [9912, 10015], "\n                  Thank you for contacting us. We&#39;ll get back to you within 24 hours.\n                ", [9912, 10015], "\n                  Thank you for contacting us. We&rsquo;ll get back to you within 24 hours.\n                ", [12893, 13001], "\n              Are you passionate about education? We&apos;re always looking for dedicated educators\n            ", [12893, 13001], "\n              Are you passionate about education? We&lsquo;re always looking for dedicated educators\n            ", [12893, 13001], "\n              Are you passionate about education? We&#39;re always looking for dedicated educators\n            ", [12893, 13001], "\n              Are you passionate about education? We&rsquo;re always looking for dedicated educators\n            ", [902, 1091], "\n              Nurturing young minds with excellence in education. From Pre-Primary to Class 12,\n              we provide quality CBSE education that shapes tomorrow&apos;s leaders.\n            ", [902, 1091], "\n              Nurturing young minds with excellence in education. From Pre-Primary to Class 12,\n              we provide quality CBSE education that shapes tomorrow&lsquo;s leaders.\n            ", [902, 1091], "\n              Nurturing young minds with excellence in education. From Pre-Primary to Class 12,\n              we provide quality CBSE education that shapes tomorrow&#39;s leaders.\n            ", [902, 1091], "\n              Nurturing young minds with excellence in education. From Pre-Primary to Class 12,\n              we provide quality CBSE education that shapes tomorrow&rsquo;s leaders.\n            ", [5891, 6066], "\n                Highly qualified and experienced teachers dedicated to nurturing\n                each student&apos;s potential and providing personalized attention.\n              ", [5891, 6066], "\n                Highly qualified and experienced teachers dedicated to nurturing\n                each student&lsquo;s potential and providing personalized attention.\n              ", [5891, 6066], "\n                Highly qualified and experienced teachers dedicated to nurturing\n                each student&#39;s potential and providing personalized attention.\n              ", [5891, 6066], "\n                Highly qualified and experienced teachers dedicated to nurturing\n                each student&rsquo;s potential and providing personalized attention.\n              ", [8600, 8696], "\n              Take the first step towards your child&apos;s bright future. Apply today!\n            ", [8600, 8696], "\n              Take the first step towards your child&lsquo;s bright future. Apply today!\n            ", [8600, 8696], "\n              Take the first step towards your child&#39;s bright future. Apply today!\n            ", [8600, 8696], "\n              Take the first step towards your child&rsquo;s bright future. Apply today!\n            ", [6774, 6932], "\n              Your dedicated space for school communications, updates, and resources \n              to support your child&apos;s educational journey.\n            ", [6774, 6932], "\n              Your dedicated space for school communications, updates, and resources \n              to support your child&lsquo;s educational journey.\n            ", [6774, 6932], "\n              Your dedicated space for school communications, updates, and resources \n              to support your child&#39;s educational journey.\n            ", [6774, 6932], "\n              Your dedicated space for school communications, updates, and resources \n              to support your child&rsquo;s educational journey.\n            ", [10492, 10531], "Track your child&apos;s academic performance", [10492, 10531], "Track your child&lsquo;s academic performance", [10492, 10531], "Track your child&#39;s academic performance", [10492, 10531], "Track your child&rsquo;s academic performance", [20781, 20847], "\n                          Child&apos;s Name *\n                        ", [20781, 20847], "\n                          Child&lsquo;s Name *\n                        ", [20781, 20847], "\n                          Child&#39;s Name *\n                        ", [20781, 20847], "\n                          Child&rsquo;s Name *\n                        ", [914, 917], "unknown", [914, 917], "never", [1458, 1461], [1458, 1461], [2480, 2483], [2480, 2483], [3099, 3102], [3099, 3102], [4600, 4786], "\n              Nurturing young minds with quality education, values, and holistic development \n              from Pre-Primary to Class 12. Building tomorrow&apos;s leaders today.\n            ", [4600, 4786], "\n              Nurturing young minds with quality education, values, and holistic development \n              from Pre-Primary to Class 12. Building tomorrow&lsquo;s leaders today.\n            ", [4600, 4786], "\n              Nurturing young minds with quality education, values, and holistic development \n              from Pre-Primary to Class 12. Building tomorrow&#39;s leaders today.\n            ", [4600, 4786], "\n              Nurturing young minds with quality education, values, and holistic development \n              from Pre-Primary to Class 12. Building tomorrow&rsquo;s leaders today.\n            "]