exports.id=158,exports.ids=[158],exports.modules={279:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},1135:()=>{},1891:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var a=t(687),l=t(5814),r=t.n(l),n=t(3210),i=t(6510),d=t(1836);let c=[{name:"Home",href:"/"},{name:"About Us",href:"/about"},{name:"Academics",href:"/academics"},{name:"Activities",href:"/activities"},{name:"Admissions",href:"/admissions"},{name:"Faculty",href:"/faculty"},{name:"Gallery",href:"/gallery"},{name:"News & Events",href:"/news-events"},{name:"Student Corner",href:"/student-corner"},{name:"Parent Zone",href:"/parent-zone"},{name:"Contact",href:"/contact"}];function o(){let[e,s]=(0,n.useState)(!1);return(0,a.jsxs)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:[(0,a.jsx)("nav",{className:"mx-auto max-w-7xl px-6 lg:px-8","aria-label":"Top",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsx)("div",{className:"flex lg:flex-1",children:(0,a.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,a.jsx)("span",{className:"sr-only",children:"Sarasvati School"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"S"})}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Sarasvati School"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Excellence in Education"})]})]})]})}),(0,a.jsx)("div",{className:"hidden lg:flex lg:gap-x-8",children:c.map(e=>(0,a.jsx)(r(),{href:e.href,className:"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors duration-200",children:e.name},e.name))}),(0,a.jsxs)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4",children:[(0,a.jsx)(r(),{href:"/admissions",className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 transition-colors duration-200",children:"Apply Now"}),(0,a.jsx)(r(),{href:"/contact",className:"rounded-md border border-gray-300 px-4 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50 transition-colors duration-200",children:"Contact Us"})]}),(0,a.jsx)("div",{className:"flex lg:hidden",children:(0,a.jsxs)("button",{type:"button",className:"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700",onClick:()=>s(!0),children:[(0,a.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,a.jsx)(i.A,{className:"h-6 w-6","aria-hidden":"true"})]})})]})}),e&&(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"fixed inset-0 z-50"}),(0,a.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,a.jsx)("span",{className:"sr-only",children:"Sarasvati School"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"h-8 w-8 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"S"})}),(0,a.jsx)("div",{children:(0,a.jsx)("h1",{className:"text-lg font-bold text-gray-900",children:"Sarasvati School"})})]})]}),(0,a.jsxs)("button",{type:"button",className:"-m-2.5 rounded-md p-2.5 text-gray-700",onClick:()=>s(!1),children:[(0,a.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,a.jsx)(d.A,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,a.jsx)("div",{className:"mt-6 flow-root",children:(0,a.jsxs)("div",{className:"-my-6 divide-y divide-gray-500/10",children:[(0,a.jsx)("div",{className:"space-y-2 py-6",children:c.map(e=>(0,a.jsx)(r(),{href:e.href,className:"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50",onClick:()=>s(!1),children:e.name},e.name))}),(0,a.jsxs)("div",{className:"py-6 space-y-2",children:[(0,a.jsx)(r(),{href:"/admissions",className:"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-white bg-blue-600 hover:bg-blue-500",onClick:()=>s(!1),children:"Apply Now"}),(0,a.jsx)(r(),{href:"/contact",className:"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 border border-gray-300 hover:bg-gray-50",onClick:()=>s(!1),children:"Contact Us"})]})]})})]})]})]})}},3327:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},4597:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Header.tsx","default")},6532:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,4597))},8042:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h,metadata:()=>m});var a=t(7413),l=t(5091),r=t.n(l);t(1135);var n=t(4597),i=t(4536),d=t.n(i);let c={main:[{name:"About Us",href:"/about"},{name:"Academics",href:"/academics"},{name:"Admissions",href:"/admissions"},{name:"Faculty",href:"/faculty"},{name:"Gallery",href:"/gallery"},{name:"Contact",href:"/contact"}],student:[{name:"Student Corner",href:"/student-corner"},{name:"Timetables",href:"/student-corner#timetables"},{name:"Syllabus",href:"/student-corner#syllabus"},{name:"E-Learning",href:"/student-corner#elearning"}],parent:[{name:"Parent Zone",href:"/parent-zone"},{name:"Notices",href:"/parent-zone#notices"},{name:"Circulars",href:"/parent-zone#circulars"},{name:"Feedback",href:"/parent-zone#feedback"}],social:[{name:"Facebook",href:"#",icon:e=>(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z",clipRule:"evenodd"})})},{name:"Instagram",href:"#",icon:e=>(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.253 14.894 3.762 13.743 3.762 12.446s.49-2.448 1.364-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.608c-.807 0-1.297-.49-1.297-1.297s.49-1.297 1.297-1.297 1.297.49 1.297 1.297-.49 1.297-1.297 1.297zm-7.83 1.297c1.297 0 2.448.49 3.323 1.297.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.875-.875-1.297-2.026-1.297-3.323s.422-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297z",clipRule:"evenodd"})})},{name:"Twitter",href:"#",icon:e=>(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...e,children:(0,a.jsx)("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})},{name:"YouTube",href:"#",icon:e=>(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z",clipRule:"evenodd"})})}]};function o(){return(0,a.jsxs)("footer",{className:"bg-gray-900","aria-labelledby":"footer-heading",children:[(0,a.jsx)("h2",{id:"footer-heading",className:"sr-only",children:"Footer"}),(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32",children:[(0,a.jsxs)("div",{className:"xl:grid xl:grid-cols-3 xl:gap-8",children:[(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"S"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-white",children:"Sarasvati School"}),(0,a.jsx)("p",{className:"text-sm text-gray-300",children:"Excellence in Education"})]})]}),(0,a.jsx)("p",{className:"text-sm leading-6 text-gray-300",children:"Nurturing young minds with quality education, values, and holistic development from Pre-Primary to Class 12. Building tomorrow's leaders today."}),(0,a.jsx)("div",{className:"flex space-x-6",children:c.social.map(e=>(0,a.jsxs)("a",{href:e.href,className:"text-gray-400 hover:text-gray-300",children:[(0,a.jsx)("span",{className:"sr-only",children:e.name}),(0,a.jsx)(e.icon,{className:"h-6 w-6","aria-hidden":"true"})]},e.name))})]}),(0,a.jsxs)("div",{className:"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0",children:[(0,a.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Quick Links"}),(0,a.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:c.main.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(d(),{href:e.href,className:"text-sm leading-6 text-gray-300 hover:text-white",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{className:"mt-10 md:mt-0",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Students"}),(0,a.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:c.student.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(d(),{href:e.href,className:"text-sm leading-6 text-gray-300 hover:text-white",children:e.name})},e.name))})]})]}),(0,a.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Parents"}),(0,a.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:c.parent.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(d(),{href:e.href,className:"text-sm leading-6 text-gray-300 hover:text-white",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{className:"mt-10 md:mt-0",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Contact Info"}),(0,a.jsxs)("div",{className:"mt-6 space-y-4 text-sm text-gray-300",children:[(0,a.jsxs)("p",{children:["123 Education Street",(0,a.jsx)("br",{}),"Knowledge City, KC 12345"]}),(0,a.jsx)("p",{children:"Phone: +91 98765 43210"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"}),(0,a.jsx)("p",{children:"Office Hours: Mon-Fri 8:00 AM - 4:00 PM"})]})]})]})]})]}),(0,a.jsx)("div",{className:"mt-16 border-t border-gray-700 pt-8 sm:mt-20 lg:mt-24",children:(0,a.jsxs)("div",{className:"md:flex md:items-center md:justify-between",children:[(0,a.jsxs)("div",{className:"flex space-x-6 md:order-2",children:[(0,a.jsx)(d(),{href:"/privacy",className:"text-sm leading-6 text-gray-300 hover:text-white",children:"Privacy Policy"}),(0,a.jsx)(d(),{href:"/terms",className:"text-sm leading-6 text-gray-300 hover:text-white",children:"Terms of Service"})]}),(0,a.jsx)("p",{className:"mt-8 text-xs leading-5 text-gray-400 md:order-1 md:mt-0",children:"\xa9 2024 Sarasvati School. All rights reserved."})]})})]})]})}let m={title:"Sarasvati School - Excellence in Education",description:"Sarasvati School provides quality education from Pre-Primary to Class 12 with CBSE curriculum, fostering academic excellence and holistic development."};function h({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:`${r().variable} font-sans antialiased`,children:(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(n.default,{}),(0,a.jsx)("main",{className:"flex-grow",children:e}),(0,a.jsx)(o,{})]})})})}},9684:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,5814,23)),Promise.resolve().then(t.bind(t,1891))}};