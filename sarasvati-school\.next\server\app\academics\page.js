/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/academics/page";
exports.ids = ["app/academics/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Facademics%2Fpage&page=%2Facademics%2Fpage&appPaths=%2Facademics%2Fpage&pagePath=private-next-app-dir%2Facademics%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Facademics%2Fpage&page=%2Facademics%2Fpage&appPaths=%2Facademics%2Fpage&pagePath=private-next-app-dir%2Facademics%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/academics/page.tsx */ \"(rsc)/./src/app/academics/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'academics',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/academics/page\",\n        pathname: \"/academics\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Facademics%2Fpage&page=%2Facademics%2Fpage&appPaths=%2Facademics%2Fpage&pagePath=private-next-app-dir%2Facademics%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(rsc)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTVFVEWSU1QyU1Q1Byb2plY3RzJTVDJTVDU2Nob29sJTVDJTVDc2FyYXN2YXRpLXNjaG9vbCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDU1RVRFklNUMlNUNQcm9qZWN0cyU1QyU1Q1NjaG9vbCU1QyU1Q3NhcmFzdmF0aS1zY2hvb2wlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDU1RVRFklNUMlNUNQcm9qZWN0cyU1QyU1Q1NjaG9vbCU1QyU1Q3NhcmFzdmF0aS1zY2hvb2wlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDU1RVRFklNUMlNUNQcm9qZWN0cyU1QyU1Q1NjaG9vbCU1QyU1Q3NhcmFzdmF0aS1zY2hvb2wlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDSGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFnTDtBQUNoTDtBQUNBLGtLQUE0SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFNUVURZXFxcXFByb2plY3RzXFxcXFNjaG9vbFxcXFxzYXJhc3ZhdGktc2Nob29sXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFNUVURZXFxcXFByb2plY3RzXFxcXFNjaG9vbFxcXFxzYXJhc3ZhdGktc2Nob29sXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXEhlYWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxTVFVEWVxcUHJvamVjdHNcXFNjaG9vbFxcc2FyYXN2YXRpLXNjaG9vbFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/academics/page.tsx":
/*!************************************!*\
  !*** ./src/app/academics/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AcademicsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,BookOpenIcon,CalculatorIcon,ChartBarIcon,ClipboardDocumentCheckIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,BookOpenIcon,CalculatorIcon,ChartBarIcon,ClipboardDocumentCheckIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/BookOpenIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,BookOpenIcon,CalculatorIcon,ChartBarIcon,ClipboardDocumentCheckIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,BookOpenIcon,CalculatorIcon,ChartBarIcon,ClipboardDocumentCheckIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,BookOpenIcon,CalculatorIcon,ChartBarIcon,ClipboardDocumentCheckIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,BookOpenIcon,CalculatorIcon,ChartBarIcon,ClipboardDocumentCheckIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/CalculatorIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,BookOpenIcon,CalculatorIcon,ChartBarIcon,ClipboardDocumentCheckIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n\n\nconst standards = [\n    {\n        level: \"Pre-Primary\",\n        grades: \"Nursery, LKG, UKG\",\n        ages: \"3-5 years\",\n        focus: \"Play-based learning, Motor skills, Social development\"\n    },\n    {\n        level: \"Primary\",\n        grades: \"Class I - V\",\n        ages: \"6-10 years\",\n        focus: \"Foundation subjects, Creative expression, Basic concepts\"\n    },\n    {\n        level: \"Middle School\",\n        grades: \"Class VI - VIII\",\n        ages: \"11-13 years\",\n        focus: \"Subject specialization, Critical thinking, Project work\"\n    },\n    {\n        level: \"Secondary\",\n        grades: \"Class IX - X\",\n        ages: \"14-15 years\",\n        focus: \"Board preparation, Career guidance, Skill development\"\n    },\n    {\n        level: \"Senior Secondary\",\n        grades: \"Class XI - XII\",\n        ages: \"16-17 years\",\n        focus: \"Stream selection, Competitive exam prep, College readiness\"\n    }\n];\nconst subjects = {\n    primary: [\n        \"English\",\n        \"Hindi\",\n        \"Mathematics\",\n        \"Environmental Studies\",\n        \"Computer Science\",\n        \"Art & Craft\",\n        \"Physical Education\"\n    ],\n    middle: [\n        \"English\",\n        \"Hindi\",\n        \"Mathematics\",\n        \"Science\",\n        \"Social Science\",\n        \"Computer Science\",\n        \"Sanskrit\",\n        \"Art Education\",\n        \"Physical Education\"\n    ],\n    secondary: [\n        \"English\",\n        \"Hindi\",\n        \"Mathematics\",\n        \"Science (Physics, Chemistry, Biology)\",\n        \"Social Science\",\n        \"Computer Applications\",\n        \"Sanskrit/French\",\n        \"Art Education\",\n        \"Physical Education\"\n    ],\n    senior: {\n        science: [\n            \"Physics\",\n            \"Chemistry\",\n            \"Mathematics\",\n            \"Biology/Computer Science\",\n            \"English\",\n            \"Physical Education\"\n        ],\n        commerce: [\n            \"Accountancy\",\n            \"Business Studies\",\n            \"Economics\",\n            \"Mathematics/Computer Science\",\n            \"English\",\n            \"Physical Education\"\n        ],\n        humanities: [\n            \"History\",\n            \"Geography\",\n            \"Political Science\",\n            \"Economics\",\n            \"English\",\n            \"Psychology/Sociology\"\n        ]\n    }\n};\nfunction AcademicsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-blue-50 via-white to-green-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Academic Excellence\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-6 text-lg leading-8 text-gray-600\",\n                                children: \"Comprehensive CBSE curriculum from Pre-Primary to Class XII, designed to foster intellectual growth, critical thinking, and holistic development.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                    children: \"Educational Standards\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-lg text-gray-600\",\n                                    children: \"From Pre-Primary to Class XII - A journey of continuous learning and growth\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-16 space-y-8\",\n                            children: standards.map((standard)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:col-span-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                className: \"h-6 w-6 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                                lineNumber: 66,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                            lineNumber: 65,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: standard.level\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                                    lineNumber: 69,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: standard.grades\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                                    lineNumber: 70,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:col-span-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"Age Group\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: standard.ages\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"Focus Areas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: standard.focus\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 17\n                                    }, this)\n                                }, standard.level, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                    children: \"CBSE Curriculum\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-lg text-gray-600\",\n                                    children: \"Following the Central Board of Secondary Education framework for comprehensive learning\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                            children: \"Structured Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Well-defined curriculum structure that ensures progressive learning from foundational concepts to advanced topics across all subjects.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                            children: \"Continuous Assessment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Regular evaluation through formative and summative assessments, ensuring comprehensive understanding and skill development.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto h-16 w-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                            children: \"Skill Development\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Focus on 21st-century skills including critical thinking, creativity, communication, and collaboration alongside academic excellence.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                    children: \"Subjects Offered\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-lg text-gray-600\",\n                                    children: \"Comprehensive subject offerings across all educational levels\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-16 space-y-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-semibold text-gray-900 mb-6 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Primary Level (Classes I-V)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                            children: subjects.primary.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 rounded-lg p-3 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-blue-900\",\n                                                        children: subject\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, subject, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-semibold text-gray-900 mb-6 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Middle School (Classes VI-VIII)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                            children: subjects.middle.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 rounded-lg p-3 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-green-900\",\n                                                        children: subject\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, subject, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-semibold text-gray-900 mb-6 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Secondary (Classes IX-X)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                            children: subjects.secondary.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-50 rounded-lg p-3 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-yellow-900\",\n                                                        children: subject\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, subject, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Science Stream\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: subjects.senior.science.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-blue-50 rounded p-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-blue-900\",\n                                                                children: subject\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, subject, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Commerce Stream\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: subjects.senior.commerce.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-50 rounded p-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-green-900\",\n                                                                children: subject\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, subject, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-5 w-5 text-purple-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Humanities Stream\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: subjects.senior.humanities.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-purple-50 rounded p-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-purple-900\",\n                                                                children: subject\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, subject, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-blue-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold tracking-tight text-white sm:text-4xl\",\n                                    children: \"Teaching Methodology\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-lg text-blue-100\",\n                                    children: \"Innovative approaches to make learning engaging and effective\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: \"Interactive Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-blue-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Smart classrooms with interactive whiteboards\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Hands-on experiments and practical sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Group discussions and collaborative projects\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Educational games and simulations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Field trips and educational excursions\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: \"Personalized Approach\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-blue-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Individual attention and mentoring\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Differentiated instruction methods\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Regular parent-teacher consultations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Remedial classes for struggling students\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Advanced programs for gifted learners\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: \"Technology Integration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-blue-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Digital learning platforms and resources\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Online assignments and assessments\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Virtual labs and simulations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Educational apps and software\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Coding and robotics programs\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: \"Holistic Development\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-blue-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Project-based learning approach\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Life skills and value education\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Creative arts and cultural activities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Sports and physical fitness programs\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Community service and social awareness\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                    children: \"Examination & Evaluation System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-lg text-gray-600\",\n                                    children: \"Comprehensive assessment framework following CBSE guidelines\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                            children: \"Formative Assessment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Continuous evaluation through class tests, assignments, projects, and activities.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Weekly tests and quizzes\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Project work and presentations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Practical assessments\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Participation in activities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                            children: \"Summative Assessment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Periodic examinations to evaluate comprehensive understanding.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Mid-term examinations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Annual examinations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Board examinations (X & XII)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Practical examinations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto h-16 w-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_BookOpenIcon_CalculatorIcon_ChartBarIcon_ClipboardDocumentCheckIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                            children: \"Progress Tracking\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Regular monitoring and feedback for continuous improvement.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Monthly progress reports\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Parent-teacher meetings\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Individual counseling\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Performance analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\academics\\\\page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/academics/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"56fc54f1c22b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcU1RVRFlcXFByb2plY3RzXFxTY2hvb2xcXHNhcmFzdmF0aS1zY2hvb2xcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjU2ZmM1NGYxYzIyYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Sarasvati School - Excellence in Education\",\n    description: \"Sarasvati School provides quality education from Pre-Primary to Class 12 with CBSE curriculum, fostering academic excellence and holistic development.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst navigation = {\n    main: [\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Academics',\n            href: '/academics'\n        },\n        {\n            name: 'Admissions',\n            href: '/admissions'\n        },\n        {\n            name: 'Faculty',\n            href: '/faculty'\n        },\n        {\n            name: 'Gallery',\n            href: '/gallery'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ],\n    student: [\n        {\n            name: 'Student Corner',\n            href: '/student-corner'\n        },\n        {\n            name: 'Timetables',\n            href: '/student-corner#timetables'\n        },\n        {\n            name: 'Syllabus',\n            href: '/student-corner#syllabus'\n        },\n        {\n            name: 'E-Learning',\n            href: '/student-corner#elearning'\n        }\n    ],\n    parent: [\n        {\n            name: 'Parent Zone',\n            href: '/parent-zone'\n        },\n        {\n            name: 'Notices',\n            href: '/parent-zone#notices'\n        },\n        {\n            name: 'Circulars',\n            href: '/parent-zone#circulars'\n        },\n        {\n            name: 'Feedback',\n            href: '/parent-zone#feedback'\n        }\n    ],\n    social: [\n        {\n            name: 'Facebook',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'Instagram',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.253 14.894 3.762 13.743 3.762 12.446s.49-2.448 1.364-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.608c-.807 0-1.297-.49-1.297-1.297s.49-1.297 1.297-1.297 1.297.49 1.297 1.297-.49 1.297-1.297 1.297zm-7.83 1.297c1.297 0 2.448.49 3.323 1.297.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.875-.875-1.297-2.026-1.297-3.323s.422-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'Twitter',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'YouTube',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ]\n};\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900\",\n        \"aria-labelledby\": \"footer-heading\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                id: \"footer-heading\",\n                className: \"sr-only\",\n                children: \"Footer\"\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:grid xl:grid-cols-3 xl:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Sarasvati School\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-300\",\n                                                        children: \"Excellence in Education\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm leading-6 text-gray-300\",\n                                        children: \"Nurturing young minds with quality education, values, and holistic development from Pre-Primary to Class 12. Building tomorrow's leaders today.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6\",\n                                        children: navigation.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-2 md:gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Quick Links\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.main.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-10 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.student.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-2 md:gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Parents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.parent.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 140,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-10 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Contact Info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-6 space-y-4 text-sm text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"123 Education Street\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                        lineNumber: 150,\n                                                                        columnNumber: 42\n                                                                    }, this),\n                                                                    \"Knowledge City, KC 12345\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Phone: +91 98765 43210\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Email: <EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Office Hours: Mon-Fri 8:00 AM - 4:00 PM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 border-t border-gray-700 pt-8 sm:mt-20 lg:mt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:flex md:items-center md:justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-6 md:order-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/privacy\",\n                                            className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/terms\",\n                                            className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                            children: \"Terms of Service\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-8 text-xs leading-5 text-gray-400 md:order-1 md:mt-0\",\n                                    children: \"\\xa9 2024 Sarasvati School. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Header.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSTUDY%5C%5CProjects%5C%5CSchool%5C%5Csarasvati-school%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigation = [\n    {\n        name: 'Home',\n        href: '/'\n    },\n    {\n        name: 'About Us',\n        href: '/about'\n    },\n    {\n        name: 'Academics',\n        href: '/academics'\n    },\n    {\n        name: 'Activities',\n        href: '/activities'\n    },\n    {\n        name: 'Admissions',\n        href: '/admissions'\n    },\n    {\n        name: 'Faculty',\n        href: '/faculty'\n    },\n    {\n        name: 'Gallery',\n        href: '/gallery'\n    },\n    {\n        name: 'News & Events',\n        href: '/news-events'\n    },\n    {\n        name: 'Student Corner',\n        href: '/student-corner'\n    },\n    {\n        name: 'Parent Zone',\n        href: '/parent-zone'\n    },\n    {\n        name: 'Contact',\n        href: '/contact'\n    }\n];\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                \"aria-label\": \"Top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"-m-1.5 p-1.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Sarasvati School\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden sm:block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-gray-900\",\n                                                        children: \"Sarasvati School\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: \"Excellence in Education\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex lg:gap-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors duration-200\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/admissions\",\n                                    className: \"rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 transition-colors duration-200\",\n                                    children: \"Apply Now\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    className: \"rounded-md border border-gray-300 px-4 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50 transition-colors duration-200\",\n                                    children: \"Contact Us\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\",\n                                onClick: ()=>setMobileMenuOpen(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        className: \"-m-1.5 p-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Sarasvati School\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-bold\",\n                                                            children: \"S\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: \"Sarasvati School\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"-m-2.5 rounded-md p-2.5 text-gray-700\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close menu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-6 w-6\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flow-root\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"-my-6 divide-y divide-gray-500/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 py-6\",\n                                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: item.href,\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: item.name\n                                                }, item.name, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/admissions\",\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-white bg-blue-600 hover:bg-blue-500\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Apply Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 border border-gray-300 hover:bg-gray-50\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\STUDY\\\\Projects\\\\School\\\\sarasvati-school\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Facademics%2Fpage&page=%2Facademics%2Fpage&appPaths=%2Facademics%2Fpage&pagePath=private-next-app-dir%2Facademics%2Fpage.tsx&appDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSTUDY%5CProjects%5CSchool%5Csarasvati-school&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();