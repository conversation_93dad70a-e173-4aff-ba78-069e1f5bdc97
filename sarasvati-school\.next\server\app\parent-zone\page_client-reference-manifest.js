globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/parent-zone/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(ssr)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/contact/page.tsx":{"*":{"id":"(ssr)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/activities/page.tsx":{"*":{"id":"(ssr)/./src/app/activities/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admissions/page.tsx":{"*":{"id":"(ssr)/./src/app/admissions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/gallery/page.tsx":{"*":{"id":"(ssr)/./src/app/gallery/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/news-events/page.tsx":{"*":{"id":"(ssr)/./src/app/news-events/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/student-corner/page.tsx":{"*":{"id":"(ssr)/./src/app/student-corner/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/parent-zone/page.tsx":{"*":{"id":"(ssr)/./src/app/parent-zone/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\components\\Header.tsx":{"id":"(app-pages-browser)/./src/components/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\contact\\page.tsx":{"id":"(app-pages-browser)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\activities\\page.tsx":{"id":"(app-pages-browser)/./src/app/activities/page.tsx","name":"*","chunks":[],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\admissions\\page.tsx":{"id":"(app-pages-browser)/./src/app/admissions/page.tsx","name":"*","chunks":[],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\gallery\\page.tsx":{"id":"(app-pages-browser)/./src/app/gallery/page.tsx","name":"*","chunks":[],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\news-events\\page.tsx":{"id":"(app-pages-browser)/./src/app/news-events/page.tsx","name":"*","chunks":[],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\student-corner\\page.tsx":{"id":"(app-pages-browser)/./src/app/student-corner/page.tsx","name":"*","chunks":[],"async":false},"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\parent-zone\\page.tsx":{"id":"(app-pages-browser)/./src/app/parent-zone/page.tsx","name":"*","chunks":["app/parent-zone/page","static/chunks/app/parent-zone/page.js"],"async":false}},"entryCSSFiles":{"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\":[],"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\page":[],"D:\\STUDY\\Projects\\School\\sarasvati-school\\src\\app\\parent-zone\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(rsc)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/contact/page.tsx":{"*":{"id":"(rsc)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/activities/page.tsx":{"*":{"id":"(rsc)/./src/app/activities/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admissions/page.tsx":{"*":{"id":"(rsc)/./src/app/admissions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/gallery/page.tsx":{"*":{"id":"(rsc)/./src/app/gallery/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/news-events/page.tsx":{"*":{"id":"(rsc)/./src/app/news-events/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/student-corner/page.tsx":{"*":{"id":"(rsc)/./src/app/student-corner/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/parent-zone/page.tsx":{"*":{"id":"(rsc)/./src/app/parent-zone/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}